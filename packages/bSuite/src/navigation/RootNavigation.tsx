import React, { useEffect } from "react";
import { StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { NavigationContainer } from "@react-navigation/native";
import { useAuthContext } from "../contexts/AuthContext";

// Components
import LoginHOC from "../containers/LoginHOC";
import { routeNames } from "./routeNames";

//Styling
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import BenefitIconSet from "engine/src/assets/fonts/icons";

import MapRoutesHOC from "../containers/MapRoutesHOC";
import TabsNavigation from "./TabsNavigaton";
import UserFlowScreenHOC from "../containers/UserFlowScreenHOC";

const Stack = createNativeStackNavigator();

const RootNavigation = () => {
  const { token, getToken } = useAuthContext();
  const { styles } = useThemeAwareObject(createStyles);

  useEffect(() => {
    (async () => {
      await getToken();
    })();
  }, []);

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName={routeNames.TABS_NAVIGATION}
          screenOptions={{ headerShown: false }}>
          <Stack.Screen
            name={routeNames.USER_FLOW_SCREEN_HOC}
            component={UserFlowScreenHOC}
            options={{ headerShown: false }}
          />
          {token ? (
            <Stack.Group>
              <Stack.Screen
                name={routeNames.TABS_NAVIGATION}
                component={TabsNavigation}
              />
              <Stack.Screen
                name={routeNames.FLEET_MAP_ROUTES_HOC}
                component={MapRoutesHOC}
                options={{
                  orientation: "default",
                }}
              />
            </Stack.Group>
          ) : (
            <Stack.Group>
              <Stack.Screen
                name={routeNames.LOGIN}
                component={LoginHOC}
                options={{ headerShown: false }}
              />
            </Stack.Group>
          )}
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaView>
  );
};

export default RootNavigation;

const createStyles = ({ color, images }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.HEADER_COLOR,
    },
  });

  return { styles, color, images };
};
