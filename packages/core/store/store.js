import { configureStore } from "@reduxjs/toolkit";
import ReduxPersistStorage from "redux-persist-filesystem-storage";
import main from "core/cycles/cycles";
import { run } from "@cycle/run";
import { createCycleMiddleware } from "redux-cycles";
import { makeHTTPDriver } from "@cycle/http";
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from "redux-persist";
import env from "react-native-config";

import Reactotron from "engine/ReactotronConfig";
import reducers from "../reducers";

const persistConfig = {
  timeout: 10000,
  key: env.APP_BUNDLE,
  storage: ReduxPersistStorage,
  whitelist: ["persist"],
  // blacklist: ['auth'],
  version: 0,
};

const persistedReducer = persistReducer(persistConfig, reducers);

let store = null;

const middleWare = [];

const cycleMiddleware = createCycleMiddleware();
const { makeActionDriver, makeStateDriver } = cycleMiddleware;

middleWare.push(cycleMiddleware);

if (__DEV__) {
  store = configureStore({
    reducer: persistedReducer,
    enhancers: [Reactotron.createEnhancer()],
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        },
      }).concat(middleWare),
  });
} else {
  store = configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        },
      }).concat(middleWare),
  });
}

const { dispatch } = store;

export const persistor = persistStore(store);

run(main, {
  ACTION: makeActionDriver(),
  STATE: makeStateDriver(),
  HTTP: makeHTTPDriver(),
});

export default store;
export { dispatch };
