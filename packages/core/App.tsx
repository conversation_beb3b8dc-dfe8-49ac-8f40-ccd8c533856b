import React from "react";
import {LogBox, <PERSON>Bar, StyleSheet, useColorScheme} from "react-native";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { QueryClient, QueryClientProvider } from "react-query";
import env from "react-native-config";
import BSeafarer<PERSON><PERSON> from "bSeafarer";
import BSuiteApp from "bSuite";
import BAuditApp from "bAudit";
import BSparePartsApp from "bSpareParts";
import BTeam from "bTeam";
import BSignature from "bSignature";
import { Provider, ReactReduxContext } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import Toast from "react-native-toast-message";

//Store
import store, { persistor } from "./store/store";

// Contexts
import { ThemeProvider } from "btheme/themeContext";
import {
  ThemeProvider as UILibThmeProvider,
  LIGHT_THEME as bUiLightTheme,
  DARK_THEME as bUiDarkTheme,
} from "b-ui-lib";

//Styling
import { Theme } from "btheme";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import { LIGHT_THEME } from "btheme/LightTheme";
import { DARK_THEME } from "btheme/DarkTheme";

const queryClient = new QueryClient();

const App = () => {
  const colorScheme = useColorScheme();
  const { styles, color } = useThemeAwareObject(createStyles);
  const APP_BUNDLE = env.APP_BUNDLE;

  // LogBox.ignoreAllLogs(); // Hide all log notifications for test purposes

  return (
    <SafeAreaProvider style={styles.safeAreaProvider}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={color.HEADER_COLOR}
      />

      <Provider store={store} context={ReactReduxContext}>
        <PersistGate loading={null} persistor={persistor}>
          <QueryClientProvider client={queryClient}>
            {APP_BUNDLE === "bTeam" || APP_BUNDLE === "bSignature" ? (
              <UILibThmeProvider
                initial={colorScheme === "light" ? bUiLightTheme : bUiDarkTheme}
              >
                {APP_BUNDLE === "bTeam" && <BTeam />}
                {APP_BUNDLE === "bSignature" && <BSignature />}

                {/*// The toast should be last*/}
                <Toast />
              </UILibThmeProvider>
            ) : (
              <ThemeProvider
                initial={colorScheme === "light" ? LIGHT_THEME : DARK_THEME}
              >
                {APP_BUNDLE === "bSeafarer" && <BSeafarerApp />}
                {APP_BUNDLE === "bSuite" && <BSuiteApp />}
                {APP_BUNDLE === "bAudit" && <BAuditApp />}
                {APP_BUNDLE === "bSpareParts" && <BSparePartsApp />}

                {/*// The toast should be last*/}
                <Toast />
              </ThemeProvider>
            )}
          </QueryClientProvider>
        </PersistGate>
      </Provider>
    </SafeAreaProvider>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    safeAreaProvider: {
      flex: 1,
    },
  });

  return { styles, color };
};

export default App;
