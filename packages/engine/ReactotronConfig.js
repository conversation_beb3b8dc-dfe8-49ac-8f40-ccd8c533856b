import Reactotron from 'reactotron-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {NativeModules, TurboModuleRegistry} from 'react-native';
import {reactotronRedux} from 'reactotron-redux';
import {Platform} from 'react-native';

if (__DEV__) {
  if (Platform.OS === 'ios') {
    let scriptHostname = 'localhost';

    try {
      let scriptURL;

      // Try TurboModules approach first (React Native 0.78+)
      try {
        const SourceCode = TurboModuleRegistry.getEnforcing('SourceCode');
        scriptURL = SourceCode.getConstants().scriptURL;
      } catch (turboError) {
        // Fall back to traditional NativeModules approach
        scriptURL = NativeModules.SourceCode?.scriptURL;
      }

      if (scriptURL && typeof scriptURL === 'string') {
        scriptHostname = scriptURL.split('://')[1].split(':')[0];
      }
    } catch (error) {
      console.warn('Failed to get script URL, using localhost:', error);
    }

    Reactotron.configure({host: scriptHostname}) // controls connection & communication settings
      .useReactNative() // add all built-in react native plugins
      .use(reactotronRedux())
      .setAsyncStorageHandler(AsyncStorage) // AsyncStorage would either come from `react-native` or `@react-native-community/async-storage` depending on where you get it from
      .connect(); // let's connect!
  } else {
    Reactotron.configure() // controls connection & communication settings
      .useReactNative() // add all built-in react native plugins
      .use(reactotronRedux())
      .setAsyncStorageHandler(AsyncStorage) // AsyncStorage would either come from `react-native` or `@react-native-community/async-storage` depending on where you get it from
      .connect(); // let's connect!
  }
}

export default Reactotron;
