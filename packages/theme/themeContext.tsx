import React, {
  createContext,
  memo,
  useCallback,
  useMemo,
  useState,
  useEffect,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Theme } from "./ThemeInterface";
import { DARK_THEME } from "./DarkTheme";
import { LIGHT_THEME } from "./LightTheme";
import { Appearance, useColorScheme } from "react-native";

// Our context provider will provide this object shape
type ProvidedValue = {
  theme: Theme;
  changeTheme: () => void;
};

// Creating our context
// Important: the defined object here is only received by the
// consumer components when there is no rendered context provider
// in the view hierarchy, so basically it will provide
// a fallback object
const Context = createContext<ProvidedValue>({
  theme: LIGHT_THEME,
  changeTheme: () => {
    console.log("ThemeProvider is not rendered!");
  },
});

// Because our stateful context provider will be a React component
// we can define some props for it too
type Props = {
  initial: Theme;
  children?: React.ReactNode;
};

// Creating our stateful context provider
// We are using React.memo for optimization
export const ThemeProvider = memo<Props>((props) => {
  // Store the actual theme as an internal state of the provider
  const [theme, setTheme] = useState<Theme>(props.initial);
  let scheme = useColorScheme();

  useEffect(() => {
    if (theme.id === "system") {
      // Assuming each theme object has an "id" property
      if (scheme === "dark") {
        setTheme(DARK_THEME);
      } else {
        setTheme(LIGHT_THEME);
      }
    }
  }, [scheme]);

  useEffect(() => {
    const updateThemeBasedOnSystem = async () => {
      const storedTheme = await AsyncStorage.getItem("@selectedTheme");

      if (storedTheme === "system") {
        const currentColorScheme = Appearance.getColorScheme();
        if (currentColorScheme === "light") {
          setTheme(LIGHT_THEME);
        } else if (currentColorScheme === "dark") {
          setTheme(DARK_THEME);
        }
      }
    };

    // Call immediately to set theme based on current system setting
    updateThemeBasedOnSystem();

    const handleAppearanceChange = async ({ colorScheme }) => {
      const storedTheme = await AsyncStorage.getItem("@selectedTheme");
      if (storedTheme !== "system") return;

      if (colorScheme === "light") {
        setTheme(LIGHT_THEME);
      } else if (colorScheme === "dark") {
        setTheme(DARK_THEME);
      }
    };

    // Add listener to Appearance changes
    Appearance.addChangeListener(handleAppearanceChange);

    return () => {
      // Cleanup listener
      // Appearance?.remove();
    };
  }, []);

  const ToggleThemeCallback = useCallback(
    (selectedTheme: string) => {
      saveSelectedThemeToStorage(selectedTheme);

      if (selectedTheme === "system") {
        if (scheme === "light") {
          return setTheme(LIGHT_THEME);
        }
        if (scheme === "dark") {
          return setTheme(DARK_THEME);
        }
      }
      if (selectedTheme === "light") {
        return setTheme(LIGHT_THEME);
      }
      if (selectedTheme === "dark") {
        return setTheme(DARK_THEME);
      }
      return;
    },
    [scheme]
  );

  // Building up the provided object
  // We're using the React.useMemo hook for optimization
  const MemoizedValue = useMemo(() => {
    const value: ProvidedValue = {
      theme,
      changeTheme: ToggleThemeCallback,
    };

    return value;
  }, [theme]);

  useEffect(() => {
    getSelectedThemeToStorage();
  }, []);

  const getSelectedThemeToStorage = async () => {
    try {
      const value = await AsyncStorage.getItem("@selectedTheme");

      if (value !== null) {
        ToggleThemeCallback(value);
      }
    } catch (e) {
      console.log("Error retrieving selectedCompany from AsyncStorage: ", e);
    }
  };

  const saveSelectedThemeToStorage = async (selectedTheme: string) => {
    try {
      await AsyncStorage.setItem("@selectedTheme", selectedTheme);
    } catch (e) {
      console.log("Selected Theme failed to be stored in AsyncStorage", e);
    }
  };

  // Render our context provider by passing it the value to provide
  return (
    <Context.Provider value={MemoizedValue}>{props.children}</Context.Provider>
  );
});

// Creating a custom context consumer hook for function components
export const useTheme = () => React.useContext(Context);
