import React, { useCallback, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  FlatList,
  Image,
  Platform,
  StyleSheet,
  View,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { launchImageLibrary } from "react-native-image-picker";
import { deleteAttachment, setAttachments } from "../../../core/reducers";
import { pick, types, errorCodes, isErrorWithCode } from "@react-native-documents/picker";
import RNFS from "react-native-fs";
import { Video as VideoCompressor } from "react-native-compressor";

import RouteNames from "../navigation/RouteNames";
import { GuidelineContext } from "../navigation/GuidelinesNavigator";

//Styling
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

// Components
import {
  CustomText,
  NavigatorCard,
  MyModal,
  TitleGroupedCards,
} from "bcomponents";
import Attachment from "../components/attachment";
import CompressionProgress from "../components/camera/compressionProgress";

const Attachments = () => {
  const { styles, color, images } = useThemeAwareObject(createStyles);
  const { attachments: storeAttachments, inspections } = useSelector(
    (state) => state.persist.questionReducer
  );
  const dispatch = useDispatch();
  const context = React.useContext(GuidelineContext);
  const editMode = context?.editMode;
  const inspectionId = context?.inspectionId;

  const [isVisible, setIsVisible] = useState(false);
  const [compressionProgress, setCompressionProgress] = useState(0);

  const {
    attachments,
    setAttachments: setAttachmentsContext,
    addAttachments,
  } = context;

  useEffect(() => {
    _setResults();
  }, [attachments, storeAttachments]);

  const handleFilePicker = async () => {
    await pick({
      type: [
        types.csv,
        types.pdf,
        types.doc,
        types.docx,
        types.ppt,
        types.pptx,
        types.xls,
        types.xlsx,
        types.plainText,
      ],
    })
      .then(async (res) => {
        const id = Date.now();
        addAttachments(id);
        const document = res[0];
        const path = `${inspections[inspectionId]?.dirPath}/${document.name}`;

        await RNFS.copyFile(document.uri, `${path}`);

        dispatch(
          setAttachments({
            id: id,
            path: `${path}`,
            type: "document",
            name: document.name,
          })
        );
      })
      .catch((err) => {
        if (isErrorWithCode(err) && err.code === errorCodes.OPERATION_CANCELED) {
          // User cancelled the picker, exit any dialogs or menus and move on
          console.log("cancelled");
        } else {
          Alert.alert("Error", "Something went wrong");
          throw err;
        }
      });
    setIsVisible(false);
  };

  const handlePickFromGallery = async () => {
    await launchImageLibrary({
      includeBase64: false,
      maxHeight: 1080,
      maxWidth: 1920,
      quality: 0.4,
      mediaType: "mixed",
      selectionLimit: 1,
      includeExtra: true,
      videoQuality: "low",
      formatAsMp4: true,
    })
      .then(async (response) => {
        if (response.didCancel) {
          console.log("User cancelled image picker");
          return;
        }

        const id = Date.now();
        const media = await response?.assets[0];
        const fileName = await response?.assets[0].fileName;

        const newPath = `${inspections[inspectionId].dirPath}/${fileName}`;

        if (!RNFS.exists(inspections[inspectionId].dirPath)) {
          await RNFS.mkdir(inspections[inspectionId].dirPath).then(() => {
            console.log("directory created");
          });
        }

        if (media?.duration) {
          setCompressionProgress(1);

          RNFS.copyFile(media?.uri, newPath)
            .then(async () => {
              const compressedPath = await VideoCompressor.compress(
                newPath,
                {},
                (progress) => {
                  setCompressionProgress(progress);
                }
              );

              await RNFS.unlink(newPath);
              await RNFS.moveFile(compressedPath, newPath);

              addAttachments(id);

              dispatch(
                setAttachments({
                  id: id,
                  path: newPath.replace("file://", ""),
                  duration: media.duration,
                  type: "video",
                  name: media.fileName,
                })
              );

              setCompressionProgress(0);
            })
            .catch((error) => {
              console.log("error", error);
              Alert.alert("Something went wrong", error.message);
              setCompressionProgress(0);
            });
        } else {
          addAttachments(id);

          RNFS.copyFile(media.uri, newPath)
            .then(() => {
              dispatch(
                setAttachments({
                  id: id,
                  path: media.uri,
                  height: media.height,
                  width: media.width,
                  type: "photo",
                  name: fileName,
                })
              );
            })
            .catch((error) => {
              console.log("error", error);
              Alert.alert("Something went wrong", error.message);
              setCompressionProgress(0);
            });
        }
      })
      .catch((error) => {
        console.log("error image", error);
      });
    setIsVisible(false);
  };

  const _setResults = useCallback(() => {
    context.addResultsToState();
  }, [dispatch, attachments, storeAttachments]);

  const memoStoreAttachments = React.useMemo(
    () =>
      attachments.map(
        (id) => storeAttachments[id]?.id !== undefined && storeAttachments[id]
      ),
    [storeAttachments.allIds, attachments]
  );

  const deleteAttachmentFromList = async (attachmentId) => {
    const file = `${inspections[inspectionId].dirPath}/${storeAttachments[attachmentId].name}`;

    dispatch(deleteAttachment(attachmentId));
    setAttachmentsContext(
      attachments.filter((id) => {
        return id !== attachmentId;
      })
    );

    await RNFS.unlink(file)
      .then(() => {
        console.log("FILE DELETED");
      })
      .catch((err) => {
        console.log("Failed to delete FILE", err.message);
      });
  };

  const handleDeleteAttachment = (id) => {
    Alert.alert(
      "Delete Attachment",
      "This action cannot be reverted. Are you sure you want to delete?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Delete",
          onPress: () => deleteAttachmentFromList(id),
          style: "destructive",
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <View style={{ marginBottom: SPACING.S }}>
        <TitleGroupedCards title="Attachments List" />
      </View>

      <FlatList
        data={memoStoreAttachments}
        numColumns={2}
        contentContainerStyle={{ flexGrow: 1 }}
        columnWrapperStyle={{ justifyContent: "space-between" }}
        keyExtractor={(item) => item?.id}
        renderItem={({ item }) => (
          <Attachment item={item} onPress={handleDeleteAttachment} />
        )}
        ListEmptyComponent={
          <View style={{ flex: 1, justifyContent: "center" }}>
            <CustomText
              style={{
                textAlign: "center",
                color: color.TEXT_DIMMED,
                fontStyle: "italic",
              }}
            >
              No Attachments Found
            </CustomText>
          </View>
        }
      />

      {editMode && (
        <>
          <View
            style={{
              flexDirection: "row",
              backgroundColor: color.PRESSABLE,
              borderRadius: SPACING.XS,
              marginVertical: SPACING.XS,
            }}
          >
            <NavigatorCard
              style={styles.navigationCard}
              image={"folder-plus"}
              screenTitle={"Add Files"}
              onPress={() => {
                setIsVisible(true);
              }}
            />

            <NavigatorCard
              style={styles.navigationCard}
              icon={
                <Image
                  source={images.CAMERA}
                  resizeMode="contain"
                  style={{ height: 26, width: 26 }}
                />
              }
              screenName={{
                name: RouteNames.Camera,
                params: { captureType: "photo", inspectionId },
              }}
              screenTitle={"Camera"}
            />

            <NavigatorCard
              style={styles.navigationCard}
              icon={
                <Image
                  source={images.MICROPHONE}
                  resizeMode="contain"
                  style={{ height: 22, width: 22 }}
                />
              }
              screenName={{
                name: RouteNames.Audio,
                params: { captureType: "audio" },
              }}
              screenTitle={"Record"}
            />
          </View>
        </>
      )}

      <MyModal open={isVisible} close={() => setIsVisible(false)}>
        <View
          style={{
            flexDirection: "row",
            backgroundColor: color.BACKGROUND,
            flexWrap: "wrap",
            justifyContent: "space-between",
            padding: SPACING.XS,
            borderTopRightRadius: SPACING.XS,
            borderTopLeftRadius: SPACING.XS,
          }}
        >
          <NavigatorCard
            style={styles.navigationCard}
            image={"plus"}
            screenTitle={"Media"}
            onPress={handlePickFromGallery}
          />

          <NavigatorCard
            style={styles.navigationCard}
            image={"plus"}
            screenTitle={"Documents"}
            onPress={handleFilePicker}
          />
        </View>
      </MyModal>

      {compressionProgress !== 0 && (
        <CompressionProgress
          compressionProgress={compressionProgress}
          setCompressionProgress={setCompressionProgress}
        />
      )}
    </View>
  );
};

const createStyles = ({ color, images }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
      paddingHorizontal: SPACING.M,
    },
    navigationCard: {
      backgroundColor: "transparent",
    },
  });

  return { styles, color, images };
};

export default Attachments;
