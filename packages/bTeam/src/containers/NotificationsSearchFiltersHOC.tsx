import React, { useMemo } from "react";
import { StyleSheet, View } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { useThemeAwareObject, Theme } from "b-ui-lib";
import NotificationsSearchFiltersScreen from "../components/NotificationsSearchFilters/NotificationsSearchFiltersScreen";
import {
  clearNotificationsSearchFields,
  setIsQuickSearchEnabled,
  setSearchFilters,
} from "../slices/generalNotificationsSlice";
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native";
import { fetchGridNotifications } from "../slices/notificationsSlice";
import { calculateAvatarName } from "../helpers/calculateAvatarName";

export type NotificationsSearchFields = {
  searchText: string;
  createDateFrom: string;
  createDateTo: string;
  from: string[];
  participants: string[];
  notifiedUsers: string[];
  readStatus: string;
  notified: string;
  requestForReply: string;
  isStarred: string;
};

type Props = {};

const NotificationsSearchFiltersHOC: React.FC = ({}: Props) => {
  const { styles } = useThemeAwareObject(createStyles);
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();

  const { searchFilters, participantUsers, searchFiltersCounter } = useSelector(
    (state) => state.root.bTeamGeneralNotificationSlice
  );

  const { searchSuggestions } = useSelector(
    (state) => state.persist.bTeamNotificationSlice
  );

  const clearNotificationsSearchFieldsAction = () => {
    dispatch(clearNotificationsSearchFields());
  };

  const fetchGridNotificationsAction = (payload: NotificationsSearchFields) => {
    dispatch(fetchGridNotifications(payload));
  };

  const setSearchFiltersAction = (payload: NotificationsSearchFields) => {
    dispatch(setSearchFilters(payload));
  };

  const setIsQuickSearchEnabledAction = (payload: boolean) => {
    dispatch(setIsQuickSearchEnabled(payload));
  };

  const mappedParticipantUsers = useMemo(() => {
    return participantUsers.allIds?.map((userId) => ({
      id: userId,
      value: "",
      recipient: participantUsers.byId[userId]?.username,
      avatarName: calculateAvatarName(participantUsers.byId[userId]?.username),
      avatarBackgroundColor: "",
    }));
  }, [participantUsers]);

  return (
    <View style={styles.container}>
      <NotificationsSearchFiltersScreen
        searchFilters={searchFilters}
        setSearchFilters={setSearchFiltersAction}
        searchFiltersCounter={searchFiltersCounter}
        fetchGridNotifications={fetchGridNotificationsAction}
        clearNotificationsSearchFields={clearNotificationsSearchFieldsAction}
        setIsQuickSearchEnabled={setIsQuickSearchEnabledAction}
        navigation={navigation}
        recipientsEmails={mappedParticipantUsers}
        participantUsersById={participantUsers.byId}
        searchSuggestions={searchSuggestions}
      />
    </View>
  );
};

export default NotificationsSearchFiltersHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
  });

  return { styles, color };
};
