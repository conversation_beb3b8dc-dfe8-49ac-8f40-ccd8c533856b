import React, { useCallback, useState } from "react";
import {
  Navigation<PERSON>rop,
  ParamListBase,
  useFocusEffect,
  useNavigation,
} from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import { getCaseProjects } from "../slices/caseProjectsSlice";
import { getCaseStatuses } from "../slices/caseStatusesSlice";
import { getCasePriorities } from "../slices/casePrioritiesSlice";
import { getCaseTypes } from "../slices/caseTypesSlice";

// Components
import CasesScreen from "../components/cases/CasesScreen";
import { fetchGridCases } from "../slices/casesSlice";
import { SCREEN_NAMES } from "../constants/screenNames";
import { ActivityIndicator, View } from "react-native";
import { CustomText, useThemeAwareObject } from "b-ui-lib";
import SearchBarAnimationHOC from "./SearchBarAnimationHOC";
import CasesSearchControls from "../navigation/headers/CasesSearchControls";

const INITIAL_PAGE_SIZE = 100;

const CasesHOC: React.FC = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { color } = useThemeAwareObject((color) => color);

  const [skipFirst, setSkipFirst] = useState(0);
  const [pageSize, setPageSize] = useState(INITIAL_PAGE_SIZE);

  const {
    casesList,
    casesListLoading,
    casesListLoadingMore,
    casesListCount,
    casesListError,
  } = useSelector((state) => state.persist.bTeamCasesSlice);

  const _handleFetchCasesList = () => dispatch(fetchGridCases(0));
  const _handleFetchProjectsList = () => dispatch(getCaseProjects());
  const _handleFetchStatusesList = () => dispatch(getCaseStatuses());
  const _handleFetchPrioritiesList = () => dispatch(getCasePriorities());
  const _handleFetchTypesList = () => dispatch(getCaseTypes());

  useFocusEffect(
    useCallback(() => {
      _handleFetchCasesList();
      _handleFetchProjectsList();
      _handleFetchStatusesList();
      _handleFetchPrioritiesList();
      _handleFetchTypesList();
    }, [])
  );

  const handleTapCase = (caseGuid) => {
    navigation.navigate(SCREEN_NAMES.case, {
      CAS_Guid: caseGuid,
      caseDate: casesList.byId[caseGuid].CAS_CreatedTimestamp,
    });
  };

  const fetchCases = async (offset: number, size: number) => {
    const params = {
      skipFirst: offset,
      pageSize: size,
    };

    await dispatch(fetchGridCases(params));
  };

  // Infinite scroll load more function
  const loadMore = () => {
    if (
      !casesListLoadingMore && // Prevent new fetch during initial load
      casesList.allIds.length < casesListCount // Ensure more data is available
    ) {
      const newSkip = skipFirst + pageSize;

      fetchCases(newSkip, pageSize).finally(() => {
        setSkipFirst(newSkip); // Update skip value after fetch
      });
    }
  };

  return (
    <SearchBarAnimationHOC searchBar={<CasesSearchControls />}>
      <CasesScreen
        listData={casesList.allIds.map((id) => casesList.byId[id])}
        handleRefreshList={_handleFetchCasesList}
        isLoading={casesListLoading}
        errorMessage={
          casesListError ? "Something went wrong:\n" + casesListError : ""
        }
        handleTapCase={handleTapCase}
        loadMore={loadMore}
        listFooterComponent={
          casesList.allIds.length < casesListCount || casesListLoadingMore ? (
            <View style={{ padding: 10 }}>
              {casesListLoadingMore && (
                <ActivityIndicator size="small" color={color.MESSAGE_FLAG} />
              )}
            </View>
          ) : (
            <View style={{ padding: 10 }}>
              {casesList.allIds?.length > 0 && (
                <CustomText style={{ textAlign: "center" }}>
                  No more items to show
                </CustomText>
              )}
            </View>
          )
        }
      />
    </SearchBarAnimationHOC>
  );
};

export default CasesHOC;
