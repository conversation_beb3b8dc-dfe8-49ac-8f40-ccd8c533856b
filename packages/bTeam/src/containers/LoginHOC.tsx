import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { clearErrorMessage, tryLogin } from "../slices/authSlice";
import { tryLoginLoading } from "../slices/generalSlice";

// Components
import LoginScreen from "../components/login/LoginScreen";

type FormData = {
  baseUrl: string;
  userName: string;
  password: string;
};

const LoginHOC: React.FC = () => {
  const dispatch = useDispatch();
  const baseUrl = useSelector((state) => state.persist.bTeamAuth.baseUrl);
  const userName = useSelector((state) => state.persist.bTeamAuth.userName);
  const password = useSelector((state) => state.persist.bTeamAuth.password);
  const isLoginLoading = useSelector(
    (state) => state.root.bTeamGeneralSlice.isLoginLoading
  );
  const loginErrorMessage = useSelector(
    (state) => state.persist.bTeamAuth.loginErrorMessage
  );

  const onSubmit = (formData: FormData) => {
    const { baseUrl, userName, password } = formData || {};

    dispatch(tryLogin({ baseUrl, userName, password }));
    dispatch(tryLoginLoading());
  };

  const clearErrorMessageAction = () => dispatch(clearErrorMessage());

  return (
    <LoginScreen
      baseUrl={baseUrl}
      userName={userName}
      password={password}
      loginErrorMessage={loginErrorMessage}
      isLoading={isLoginLoading}
      onSubmit={onSubmit}
      clearErrorMessage={clearErrorMessageAction}
    />
  );
};

export default LoginHOC;
