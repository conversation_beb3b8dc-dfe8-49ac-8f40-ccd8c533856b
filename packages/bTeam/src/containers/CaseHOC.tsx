import React, { useEffect, useMemo, useCallback } from "react";
import { StyleSheet, View, BackHandler } from "react-native";
import {
  useRoute,
  NavigationProp,
  ParamListBase,
  useNavigation,
  useFocusEffect,
} from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import { useThemeAwareObject, Theme } from "b-ui-lib";
import {
  getMessageComments,
  performMarkAsReadUnreadAction,
} from "../slices/gridMessageSlice";
import {
  clearDownloadedAttachments,
  getAttachments,
  setDownloadedAttachments,
} from "../slices/attachmentsSlice";
import { CaseDTO } from "../types/DTOs/CasesListDTO";
import {
  getCaseDetails,
  getCaseLinkedCases,
  getCaseMessages,
  getCaseTasks,
  starCase,
} from "../slices/casesSlice";
import { getMetadata } from "../slices/metadataSlice";

// Helpers
import {
  downloadMultipleAttachments,
  downloadSingleAttachment,
} from "../../../bcomponents/downloadHelpers/downloadAttachmentHelper";
import { calculateStatusLabelInfo } from "../helpers/calculateStatusLabelInfo";

// Components
import CommentsHOC from "./CommentsHOC";
import { FILE_DOWNLOAD_STATUS } from "bcomponents";
import CaseScreen from "../components/cases/CaseScreen";
import { SCREEN_NAMES } from "../constants/screenNames";
import { calculatePriorityLabelInfo } from "../helpers/calculatePriorityLabelInfo";
import { calculateProjectLabelInfo } from "../helpers/calculateProjectLabelInfo";
import { calculateCaseTypeLabelInfo } from "../helpers/calculateTypeLabelInfo";

type Props = {};

type FileDownloadStatus =
  (typeof FILE_DOWNLOAD_STATUS)[keyof typeof FILE_DOWNLOAD_STATUS];

export const selectFormattedMetadata = (metadata) => {
  return metadata.mfeAllIds.map((mfeGuid) => {
    const mfe = metadata.mfeById[mfeGuid];
    const mfl = metadata.mflById[mfe.MFE_MFL_Guid];
    const mfevList = metadata.mfevAllIds
      .map((mfevGuid) => metadata.mfevById[mfevGuid])
      .filter((mfev) => mfev.MEV_MFE_Guid === mfeGuid); // Filter MFEV linked to MFE

    return {
      id: mfeGuid,
      label: mfl?.MFL_Label || mfl?.MFL_Name || "Unknown Label",
      values: mfevList.map((mfev) => mfev.MEV_Value).join(", ") || "N/A",
    };
  });
};

const CaseHOC: React.FC = ({}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const route = useRoute();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const dispatch = useDispatch();
  const CAS_Guid = route.params?.CAS_Guid;
  const caseDate = route.params?.caseDate;

  const caseData: CaseDTO = useSelector(
    (state) => state.persist.bTeamCasesSlice.caseDetails.byId[CAS_Guid]
  );

  const {
    casesList,
    tasks,
    caseDetailsLoading,
    caseDetailsError,
    caseMessagesLoading,
    caseMessagesError,
    caseLinkedCasesLoading,
    caseLinkedCasesError,
    caseTasksLoading,
    caseTasksError,
  } = useSelector((state) => state.persist.bTeamCasesSlice);

  const { getMessageMetadataError, getMetadataLoading, metadata } = useSelector(
    (state) => state.persist.bTeamMetadataSlice
  );

  const gridMessagesById = useSelector(
    (state) => state.persist.gridMessageSlice?.gridMessages?.byId
  );

  const caseStatuses = useSelector(
    (state) => state.persist.bTeamCaseStatusesSlice?.caseStatuses
  );

  const casePriorities = useSelector(
    (state) => state.persist.bTeamCasePriotitiesSlice?.casePriorities
  );

  const caseProjects = useSelector(
    (state) => state.persist.bTeamCaseProjectsSlice?.caseProjects
  );

  const caseTypes = useSelector(
    (state) => state.persist.bTeamCaseTypesSlice?.caseTypes
  );

  const formattedMetadata = selectFormattedMetadata(metadata);
  const { token } = useSelector((state) => state.persist.bTeamAuth);
  const {
    attachments,
    downloadedAttachments,
    getAttachmentsLoading,
    getAttachmentsError,
  } = useSelector((state) => state.root.bTeamAttachmentsSlice);

  const { getMessageCommentsLoading, getMessageActionsLoading } = useSelector(
    (state) => state.persist.gridMessageSlice
  );

  const handleStarAction = () => {
    dispatch(starCase({ guid: CAS_Guid, value: !caseData?.isStarred }));
  };

  const setDownloadedAttachmentsAction = (payload) => {
    dispatch(setDownloadedAttachments(payload));
  };

  const clearDownloadedAttachmentsAction = () => {
    dispatch(clearDownloadedAttachments());
  };

  const getCaseMessagesAction = (payload: { caseId: string }) => {
    dispatch(getCaseMessages(payload));
  };

  const getCaseLinkedCasesAction = (payload: { caseId: string }) => {
    dispatch(getCaseLinkedCases(payload));
  };

  const getCaseTasksAction = (payload: { caseId: string }) => {
    dispatch(getCaseTasks(payload));
  };

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      () => {
        navigation.goBack();
        return true;
      }
    );

    return () => {
      backHandler.remove();
    };
  }, [navigation]);

  useEffect(() => {
    navigation.setOptions({
      isStarred: caseData?.isStarred,
      handleStarPress: handleStarAction,
    });
  }, [navigation, caseData]);

  useFocusEffect(
    useCallback(() => {
      if (!CAS_Guid) return;

      dispatch(getCaseDetails({ guid: CAS_Guid }));
      dispatch(getMessageComments({ guid: CAS_Guid, entityId: 1002 }));
      dispatch(getMetadata({ guid: CAS_Guid, entityId: 1002 }));
      dispatch(getAttachments({ guid: CAS_Guid, entityId: 1002 }));
      getCaseMessagesAction({ caseId: CAS_Guid });
      getCaseLinkedCasesAction({ caseId: CAS_Guid });
      getCaseTasksAction({ caseId: CAS_Guid });
    }, [CAS_Guid])
  );

  useEffect(() => {
    if (!CAS_Guid) return;

    dispatch(
      performMarkAsReadUnreadAction({
        ids: [CAS_Guid],
        mode: 6,
      })
    );
  }, [CAS_Guid]);

  const caseDateMessages = caseData?.messageIds?.map(
    (messageId: string) => gridMessagesById[messageId]
  );

  const caseLinkedCases =
    caseData?.linkedCasesIds?.map(
      (caseId: string) => casesList?.byId[caseId]
    ) ?? [];

  const mappedTasks =
    caseData?.taskIds?.map((taskId: string) => {
      const task = tasks?.byId[taskId] || {};
      const statusLabelInfo = calculateStatusLabelInfo(
        caseStatuses,
        task?.CAS_CST_Id,
        color
      );

      return {
        ...task,
        statusLabel: statusLabelInfo?.statusLabel,
        statusBackgroundColor: statusLabelInfo?.statusBackgroundColor,
      };
    }) ?? [];

  const mappedCaseData = {
    ...caseData,
    ...calculateStatusLabelInfo(caseStatuses, caseData?.statusId, color),
    ...calculatePriorityLabelInfo(casePriorities, caseData?.priority),
    ...calculateProjectLabelInfo(caseProjects, caseData?.project),
    ...calculateCaseTypeLabelInfo(caseTypes, caseData?.type),
  };

  const handleFilesDownloadStatus = (
    attachmentId: string,
    downloadStatus: FileDownloadStatus
  ) => {
    setDownloadedAttachmentsAction({
      id: attachmentId,
      status: downloadStatus,
      isLoading: downloadStatus === FILE_DOWNLOAD_STATUS.loading,
    });
  };

  const handleDownloadAttachment = async (attachmentId: string) => {
    clearDownloadedAttachments();

    await downloadSingleAttachment(
      token,
      attachmentId,
      attachments?.byId?.[attachmentId]?.name,
      handleFilesDownloadStatus
    );
  };

  const handleDownloadAllAttachments = async (attachmentIds: string[]) => {
    // Clear any existing downloaded attachments.
    clearDownloadedAttachments();

    // If there are no attachments, we can optionally return or alert.
    if (attachmentIds.length === 0) {
      console.log("No attachments found");
      return;
    }

    const fileNamesMapping: Record<string, string> = {};
    if (attachments?.byId) {
      Object.keys(attachments.byId).forEach((id) => {
        // Ensure that the object has a "name" property.
        fileNamesMapping[id] = attachments.byId[id].name;
      });
    }

    // Download all attachments.
    await downloadMultipleAttachments(
      token,
      attachmentIds,
      fileNamesMapping,
      handleFilesDownloadStatus
    );
  };

  const isAnyFileDownloadLoading = useMemo(
    () =>
      Object.values(downloadedAttachments).some(
        (status) => status === FILE_DOWNLOAD_STATUS.loading
      ),
    [downloadedAttachments]
  );

  const handleTapCase = (caseId: string) => {
    navigation.push(SCREEN_NAMES.case, {
      CAS_Guid: caseId,
      caseDate: casesList?.byId?.[caseId]?.CAS_CreatedTimestamp,
    });
  };

  const handleTapTask = (taskId: string) => {
    navigation.navigate(SCREEN_NAMES.task, {
      taskId: taskId,
      taskTitle: tasks?.byId?.[taskId]?.CAS_Title,
    });
  };

  return (
    <View style={styles.container}>
      <CommentsHOC
        guid={CAS_Guid}
        entityId="1002"
        commentIds={caseData?.commentIds}
        refreshListCallback={() =>
          dispatch(getMessageComments({ guid: CAS_Guid, entityId: 1002 }))
        }
      >
        <CaseScreen
          caseData={mappedCaseData}
          caseDateMessages={caseDateMessages}
          caseMessagesLoading={caseMessagesLoading}
          caseMessagesError={caseMessagesError}
          handleRefreshMessageList={() =>
            getCaseMessagesAction({ caseId: CAS_Guid })
          }
          caseAttachmentsIds={caseData?.attachmentIds}
          attachments={attachments}
          attachmentsCount={caseData?.attachmentIds?.length}
          downloadedAttachments={downloadedAttachments}
          caseMetadata={formattedMetadata}
          caseDetailsLoading={caseDetailsLoading}
          caseDetailsError={caseDetailsError}
          getMessageCommentsLoading={getMessageCommentsLoading}
          getMessageActionsLoading={getMessageActionsLoading}
          // getMessageCommentsError={getMessageCommentsError}
          setDownloadedAttachments={setDownloadedAttachmentsAction}
          clearDownloadedAttachments={clearDownloadedAttachmentsAction}
          handleDownloadAttachment={handleDownloadAttachment}
          handleDownloadAllAttachments={handleDownloadAllAttachments}
          isAnyFileDownloadLoading={isAnyFileDownloadLoading}
          getMessageMetadataError={getMessageMetadataError}
          getMetadataLoading={getMetadataLoading}
          caseDate={caseDate}
          caseLinkedCasesLoading={caseLinkedCasesLoading}
          caseLinkedCasesError={caseLinkedCasesError}
          handleRefreshLinkedCaseList={() =>
            getCaseLinkedCasesAction({ caseId: CAS_Guid })
          }
          caseLinkedCases={caseLinkedCases}
          handleTapCase={handleTapCase}
          tasks={mappedTasks}
          caseTasksLoading={caseTasksLoading}
          caseTasksError={caseTasksError}
          handleRefreshTaskList={() => getCaseTasksAction({ caseId: CAS_Guid })}
          handleTapTask={handleTapTask}
        />
      </CommentsHOC>
    </View>
  );
};

export default CaseHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
  });

  return { styles, color };
};
