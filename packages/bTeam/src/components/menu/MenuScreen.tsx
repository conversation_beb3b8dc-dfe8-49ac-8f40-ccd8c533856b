import React from "react";
import packageJSON from "engine/package.json";
import { StyleSheet, View } from "react-native";

// Components
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  Button,
  SPACING,
} from "b-ui-lib";

type Props = {
  logout: () => void;
};

const MenuScreen: React.FC = ({ logout }: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      <Button title="LOGOUT" onPress={logout} />

      <View style={styles.buttonsWrapper}>
        <CustomText style={styles.versionText} testID="appVersion">
          Version: {packageJSON.version}
        </CustomText>
      </View>
    </View>
  );
};

export default MenuScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
      justifyContent: "center",
      padding: 20,
    },
    buttonsWrapper: {
      alignItems: "center",
      marginBottom: SPACING.S,
    },
    versionText: {
      color: color.TEXT_DIMMED,
      marginTop: 20,
    },
  });

  return { styles, color };
};
