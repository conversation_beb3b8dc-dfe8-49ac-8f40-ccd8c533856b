import React from "react";
import { StyleSheet, View } from "react-native";
import RenderHtml from "react-native-render-html";

// Components
import { FONT_SIZES, ICON_POSITIONS, IconTextButton, SPACING } from "b-ui-lib";
import TabErrorMessage from "../../TabErrorMessage";

type Props = {
  html: string;
  width: number;
  messageBodyError: string;
  handleRetryMessageBody: () => void;
  token?: string;
};

/**
 * Surgical preprocessing function for complex email HTML
 * Fixes malformed HTML and ensures all content fits within screen width
 * Adds authentication headers to images that need them
 */
const preprocessEmailHtml = (html: string, width: number): string => {
  let processedHtml = html;

  // Step 1: Fix malformed self-closing image tags (e.g., "<img ... / style='...'>" -> "<img ... style='...' />")
  processedHtml = processedHtml.replace(
    /(<img[^>]*?)\s+\/\s+(style=["'][^"']*["'])\s*>/gi,
    "$1 $2 />"
  );
  processedHtml = processedHtml.replace(/(<img[^>]*?)\s+\/\s*>/gi, "$1 />");

  // Step 2: Fix incomplete CSS properties (e.g., "max-" -> "max-width: 100%")
  processedHtml = processedHtml.replace(
    /\bmax-\s*(?:[^;}\s]*)?/gi,
    "max-width: 100%"
  );
  processedHtml = processedHtml.replace(
    /\bmin-\s*(?:[^;}\s]*)?/gi,
    "min-width: 0"
  );

  // Step 3: Fix malformed font-family CSS
  processedHtml = processedHtml.replace(
    /font-family\s*:\s*;+\s*["']([^"']*)/gi,
    'font-family: "$1"'
  );
  processedHtml = processedHtml.replace(
    /font-family\s*:\s*;+/gi,
    "font-family: Arial, sans-serif"
  );

  // Step 4: Fix HTML structure - add missing table row/cell containers (minimal fixes only)
  processedHtml = processedHtml.replace(
    /<table([^>]*?)>\s*<img/gi,
    "<table$1><tr><td><img"
  );
  processedHtml = processedHtml.replace(
    /<table([^>]*?)>\s*([^<]+(?:<(?!\/table|tr|td)[^>]*>[^<]*<\/[^>]*>)*[^<]*)\s*<\/table>/gi,
    "<table$1><tr><td>$2</td></tr></table>"
  );

  // Step 3: Fix incomplete CSS properties (e.g., "max-" without completion)
  processedHtml = processedHtml.replace(
    /\bmax-\s*[^;}\s]*/gi,
    "max-width: 100%"
  );
  processedHtml = processedHtml.replace(/\bmin-\s*[^;}\s]*/gi, "min-width: 0");

  // Step 4: Fix malformed font-family CSS (e.g., font-family:;"Segoe UI')
  processedHtml = processedHtml.replace(
    /font-family\s*:\s*;?\s*["']([^"']*)/gi,
    'font-family: "$1"'
  );
  processedHtml = processedHtml.replace(
    /font-family\s*:\s*;?\s*([^;}\s]+)/gi,
    "font-family: $1"
  );

  // Step 5: Add aggressive text wrapping for long URLs and text
  processedHtml = processedHtml.replace(
    /(<span[^>]*style="[^"]*")/gi,
    "$1; word-break: break-all; overflow-wrap: break-word; max-width: 100%"
  );
  processedHtml = processedHtml.replace(
    /(<span[^>]*?)>/gi,
    '$1 style="word-break: break-all; overflow-wrap: break-word; max-width: 100%;">'
  );

  // Step 6: Force text wrapping on div elements
  processedHtml = processedHtml.replace(
    /(<div[^>]*style="[^"]*")/gi,
    "$1; word-break: break-all; overflow-wrap: break-word; max-width: 100%; box-sizing: border-box"
  );
  processedHtml = processedHtml.replace(
    /(<div[^>]*?)>/gi,
    '$1 style="word-break: break-all; overflow-wrap: break-word; max-width: 100%; box-sizing: border-box;">'
  );

  // Step 7: Remove all width-related style properties (but preserve max-width: 100%)
  processedHtml = processedHtml.replace(/width\s*:\s*(?!100%)[^;]+;?/gi, "");
  processedHtml = processedHtml.replace(
    /max-width\s*:\s*(?!100%)[^;]+;?/gi,
    ""
  );
  processedHtml = processedHtml.replace(/min-width\s*:\s*[^;]+;?/gi, "");

  // Step 8: Remove width attributes from all elements
  processedHtml = processedHtml.replace(/\s+width\s*=\s*["'][^"']*["']/gi, "");
  processedHtml = processedHtml.replace(/\s+width\s*=\s*[^\s>]+/gi, "");

  // Step 9: Force width: 100% on all tables and ensure proper spacing
  processedHtml = processedHtml.replace(
    /<table([^>]*?)style\s*=\s*["']([^"']*)["']/gi,
    (_match, beforeStyle, existingStyle) => {
      // Clean up existing style and add width: 100%
      let cleanStyle = existingStyle.replace(
        /width\s*:\s*(?!100%)[^;]+;?/gi,
        ""
      );
      cleanStyle = cleanStyle.replace(/max-width\s*:\s*(?!100%)[^;]+;?/gi, "");
      cleanStyle = cleanStyle.replace(/min-width\s*:\s*[^;]+;?/gi, "");
      cleanStyle = cleanStyle.trim();
      if (cleanStyle && !cleanStyle.endsWith(";")) {
        cleanStyle += ";";
      }
      return `<table${beforeStyle}style="${cleanStyle} width: 100%; max-width: 100%; table-layout: auto; box-sizing: border-box;"`;
    }
  );

  // Step 10: Handle tables without existing style attribute
  processedHtml = processedHtml.replace(
    /<table(?![^>]*style\s*=)([^>]*?)>/gi,
    '<table$1 style="width: 100%; max-width: 100%; table-layout: auto; box-sizing: border-box;">'
  );

  // Step 11: Fix td elements - add word wrapping and width constraints
  processedHtml = processedHtml.replace(
    /<td([^>]*?)style\s*=\s*["']([^"']*)["']/gi,
    (_match, beforeStyle, existingStyle) => {
      let cleanStyle = existingStyle.replace(
        /width\s*:\s*(?!100%)[^;]+;?/gi,
        ""
      );
      cleanStyle = cleanStyle.replace(/max-width\s*:\s*(?!100%)[^;]+;?/gi, "");
      cleanStyle = cleanStyle.replace(/min-width\s*:\s*[^;]+;?/gi, "");
      cleanStyle = cleanStyle.trim();
      if (cleanStyle && !cleanStyle.endsWith(";")) {
        cleanStyle += ";";
      }
      return `<td${beforeStyle}style="${cleanStyle} word-break: break-all; overflow-wrap: break-word; max-width: 100%; box-sizing: border-box;"`;
    }
  );

  // Step 12: Remove width attributes from td elements
  processedHtml = processedHtml.replace(
    /<td([^>]*?)\s+width\s*=\s*["'][^"']*["']/gi,
    "<td$1"
  );
  processedHtml = processedHtml.replace(
    /<td([^>]*?)\s+width\s*=\s*[^\s>]+/gi,
    "<td$1"
  );

  // Step 13: Handle container divs that might constrain table width
  processedHtml = processedHtml.replace(
    /<div([^>]*?)style\s*=\s*["']([^"']*)["']/gi,
    (_match, beforeStyle, existingStyle) => {
      let cleanStyle = existingStyle.replace(/width\s*:\s*[^;]+;?/gi, "");
      cleanStyle = cleanStyle.replace(/max-width\s*:\s*[^;]+;?/gi, "");
      cleanStyle = cleanStyle.replace(/min-width\s*:\s*[^;]+;?/gi, "");
      cleanStyle = cleanStyle.trim();
      if (cleanStyle && !cleanStyle.endsWith(";")) {
        cleanStyle += ";";
      }
      return `<div${beforeStyle}style="${cleanStyle}"`;
    }
  );

  // Step 14: Fix nested table issues - ensure proper structure
  processedHtml = processedHtml.replace(
    /border-spacing\s*:\s*[^;]+;?/gi,
    "border-spacing: 0;"
  );

  // Step 15: Handle cellpadding and cellspacing attributes for better mobile rendering
  processedHtml = processedHtml.replace(
    /cellpadding\s*=\s*["'][^"']*["']/gi,
    'cellpadding="0"'
  );
  processedHtml = processedHtml.replace(
    /cellspacing\s*=\s*["'][^"']*["']/gi,
    'cellspacing="0"'
  );
  processedHtml = processedHtml.replace(
    /cellpading\s*=\s*["'][^"']*["']/gi,
    'cellpadding="0"'
  ); // Fix typo in original HTML

  // Step 16: Clean up any double spaces or malformed styles
  processedHtml = processedHtml.replace(/;\s*;+/g, ";");
  processedHtml = processedHtml.replace(/style\s*=\s*["']\s*["']/gi, "");
  processedHtml = processedHtml.replace(/style\s*=\s*["']\s*;+\s*["']/gi, "");

  // Step 17: Ensure images are responsive and properly constrained
  processedHtml = processedHtml.replace(
    /<img([^>]*?)style\s*=\s*["']([^"']*)["']/gi,
    (_match, beforeStyle, existingStyle) => {
      let cleanStyle = existingStyle.replace(
        /width\s*:\s*(?!100%)[^;]+;?/gi,
        ""
      );
      cleanStyle = cleanStyle.replace(/max-width\s*:\s*(?!100%)[^;]+;?/gi, "");
      cleanStyle = cleanStyle.replace(/min-width\s*:\s*[^;]+;?/gi, "");
      cleanStyle = cleanStyle.trim();
      if (cleanStyle && !cleanStyle.endsWith(";")) {
        cleanStyle += ";";
      }
      return `<img${beforeStyle}style="${cleanStyle} max-width: 100%; height: auto; box-sizing: border-box;"`;
    }
  );

  // Step 18: Handle images without style attribute
  processedHtml = processedHtml.replace(
    /<img(?![^>]*style\s*=)([^>]*?)>/gi,
    '<img$1 style="max-width: 100%; height: auto; box-sizing: border-box;">'
  );

  // Step 19: Fix orphaned closing tags and missing opening tags
  processedHtml = processedHtml.replace(/<\/td>\s*<td/gi, "</td></tr><tr><td");
  processedHtml = processedHtml.replace(
    /<\/tr>\s*<\/table>\s*<\/td>/gi,
    "</tr></table>"
  );

  // Step 20: Force image width to screen width to prevent overflow
  processedHtml = processedHtml.replace(
    /<img([^>]*?)style\s*=\s*["']([^"']*)["']([^>]*?)>/gi,
    (_match, beforeStyle, existingStyle, afterStyle) => {
      // Clean up existing style and add width constraint
      let cleanStyle = existingStyle.replace(/width\s*:\s*[^;]+;?/gi, "");
      cleanStyle = cleanStyle.replace(/max-width\s*:\s*[^;]+;?/gi, "");
      cleanStyle = cleanStyle.replace(/min-width\s*:\s*[^;]+;?/gi, "");
      cleanStyle = cleanStyle.replace(/height\s*:\s*auto;?/gi, "");
      cleanStyle = cleanStyle.replace(/box-sizing\s*:\s*border-box;?/gi, "");
      cleanStyle = cleanStyle.trim();
      if (cleanStyle && !cleanStyle.endsWith(";")) {
        cleanStyle += ";";
      }
      return `<img${beforeStyle}style="${cleanStyle} max-width: ${width}px; height: auto; box-sizing: border-box;"${afterStyle}>`;
    }
  );

  // Handle images without existing style attribute
  processedHtml = processedHtml.replace(
    /<img(?![^>]*style\s*=)([^>]*?)>/gi,
    `<img$1 style="max-width: ${width}px; height: auto; box-sizing: border-box;">`
  );

  return processedHtml;
};

const HtmlMessageContent = ({
  html,
  width,
  messageBodyError,
  handleRetryMessageBody,
  token,
}: Props) => {
  const styles = createStyles();

  const _provideEmbeddedHeaders = (uri, tagName, params) => {
    if (tagName === "img" && uri.includes("FileLinkFileDownload")) {
      const bearerString = `Bearer ${token}`;

      return {
        Authorization: bearerString,
      };
    }
  };

  if (messageBodyError) {
    return (
      <View style={styles.errorContainer}>
        <TabErrorMessage
          text={messageBodyError}
          isVisible={Boolean(messageBodyError)}
        />

        <IconTextButton
          iconPosition={ICON_POSITIONS.left}
          iconName="Redo"
          iconSize={20}
          iconColor="#007AFF"
          title="Retry"
          onPress={handleRetryMessageBody}
          textStyle={styles.retryButtonText}
          containerStyle={styles.retryButton}
        />
      </View>
    );
  }

  // Preprocess the HTML to fix table layout issues and set image width
  const processedHtml = preprocessEmailHtml(html, width);

  return (
    <RenderHtml
      contentWidth={width}
      source={{
        html: processedHtml,
      }}
      enableExperimentalMarginCollapsing={true}
      computeEmbeddedMaxWidth={(contentWidth) => contentWidth}
      ignoredDomTags={["map", "area"]}
      renderersProps={{
        img: {
          enableExperimentalPercentWidth: true,
        },
        blockquote: {
          htmlAttribs: {
            style:
              "margin: 16px 0; padding: 16px; border-left: 4px solid #ddd; background: #f9f9f9;",
          },
        },
      }}
      provideEmbeddedHeaders={_provideEmbeddedHeaders}
      defaultTextProps={{
        style: styles.htmlTextStyle,
      }}
      tagsStyles={{
        body: {
          fontSize: FONT_SIZES.FOURTEEN,
          lineHeight: 20,
          maxWidth: width,
        },
        p: {
          marginBottom: 12,
          maxWidth: width,
        },
        div: {
          marginBottom: 8,
          maxWidth: width,
        },
        table: {
          width: width,
          maxWidth: width,
        },
        td: {
          padding: 4,
          maxWidth: width,
        },
        span: {
          maxWidth: width,
        },
        img: {
          maxWidth: width,
          height: "auto",
        },
      }}
      systemFonts={["System"]}
    />
  );
};

const createStyles = () => {
  const styles = StyleSheet.create({
    scrollContainer: {
      flex: 1,
    },
    htmlTextStyle: {
      color: "#000000",
      fontSize: FONT_SIZES.FOURTEEN,
      lineHeight: 20,
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: SPACING.L,
    },
    retryButton: {
      marginTop: SPACING.M,
      paddingHorizontal: SPACING.L,
      paddingVertical: SPACING.S,
      backgroundColor: "#FFFFFF",
      borderRadius: 8,
      borderWidth: 1,
      borderColor: "#007AFF",
    },
    retryButtonText: {
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: "600",
      color: "#007AFF",
    },
  });

  return styles;
};

export default React.memo(HtmlMessageContent);
