import React from "react";
import { Controller, useForm } from "react-hook-form";
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { TEST_IDS } from "../../constants/testIds";

// Components
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  Button,
  FONT_SIZES,
  SPACING,
  Input,
  TextWithDuration,
  Logo,
} from "b-ui-lib";

type Props = {
  baseUrl?: string;
  userName?: string;
  password?: string;
  loginErrorMessage?: string;
  isLoading: boolean;
  onSubmit: (formData: { username: string; password: string }) => void;
  clearErrorMessage: () => void;
};

const LoginScreen: React.FC = ({
  baseUrl,
  userName,
  password,
  loginErrorMessage,
  isLoading,
  onSubmit,
  clearErrorMessage,
}: Props) => {
  const { images, styles } = useThemeAwareObject(createStyles);

  const LOGIN_FIELDS = {
    baseUrl: "baseUrl",
    userName: "userName",
    password: "password",
  };

  const DEFAULT_VALUES = {
    [LOGIN_FIELDS.baseUrl]: baseUrl,
    [LOGIN_FIELDS.userName]: userName,
    [LOGIN_FIELDS.password]: password,
  };

  const { control, handleSubmit } = useForm({
    defaultValues: DEFAULT_VALUES,
  });

  const inputIos = {
    paddingVertical: SPACING.XS,
  };

  return (
    <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
      >
        <CustomText style={styles.title}>Benefit Software</CustomText>

        <View style={styles.body}>
          <Logo source={images?.B_TEAM_LOGO} style={styles.logo} />

          <View style={styles.form}>
            <View style={styles.formBody}>
              <Controller
                control={control}
                rules={{
                  required: true,
                  pattern: {
                    value: /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i,
                    message: "Enter a valid URL",
                  },
                }}
                render={({
                  field: { onChange, value },
                  fieldState: { error },
                }) => (
                  <Input
                    inputStyle={Platform.OS === "ios" ? inputIos : {}}
                    placeholder="URL"
                    value={value}
                    onChangeText={onChange}
                    error={error}
                    testID={TEST_IDS.loginInputUrl}
                  />
                )}
                name={LOGIN_FIELDS.baseUrl}
              />

              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                render={({
                  field: { onChange, value },
                  fieldState: { error },
                }) => (
                  <Input
                    inputStyle={Platform.OS === "ios" ? inputIos : {}}
                    placeholder="Username"
                    value={value}
                    onChangeText={onChange}
                    error={error}
                    testID={TEST_IDS.loginInputUsername}
                  />
                )}
                name={LOGIN_FIELDS.userName}
              />

              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                render={({
                  field: { onChange, value },
                  fieldState: { error },
                }) => (
                  <Input
                    inputStyle={
                      Platform.OS === "ios"
                        ? [inputIos, { marginRight: SPACING.S }]
                        : {}
                    }
                    placeholder="Password"
                    value={value}
                    onChangeText={onChange}
                    error={error}
                    isPassword
                    testID={TEST_IDS.loginInputPassword}
                  />
                )}
                name={LOGIN_FIELDS.password}
              />
            </View>

            <TextWithDuration
              text={loginErrorMessage}
              isError
              cbWhenComponentHide={clearErrorMessage}
              style={styles.errorText}
            />

            <Button
              title="Submit"
              onPress={handleSubmit(onSubmit)}
              containerStyle={styles.submitButton}
              isLoading={isLoading}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
};

export default LoginScreen;

const createStyles = ({ color, images }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
      padding: SPACING.TEN,
    },
    title: {
      fontSize: FONT_SIZES.TWENTY,
      textAlign: "center",
    },
    body: {
      marginTop: "auto",
      marginBottom: "auto",
    },
    logo: {
      marginBottom: SPACING.TWENTY_TWO,
      alignSelf: "center",
    },
    form: {
      backgroundColor: color.MESSAGE_BUTTONS_BACKGROUND,
      justifyContent: "center",
      padding: SPACING.S,
      borderRadius: SPACING.SIX,
    },
    formBody: {
      gap: SPACING.XS,
    },
    errorText: {
      marginTop: SPACING.L,
    },
    submitButton: {
      backgroundColor: color.BRAND_DEFAULT,
      marginTop: SPACING.XL,
    },
  });

  return { styles, images, color };
};
