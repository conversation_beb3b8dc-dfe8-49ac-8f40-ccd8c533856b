import React from "react";
import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  useWindowDimensions,
  View,
  ViewStyle,
} from "react-native";
import RenderHtml from "react-native-render-html";
import { CaseDetailsData } from "../../../types/CaseDetailsData";
import { RelatedUsersList } from "../../../types/userMails";
import { MetadataListDTO } from "../../../types/DTOs/MetadataDTO";

// Components
import { CustomText, SPACING, StatusLabel } from "b-ui-lib";

// Styles
import { Theme, useThemeAwareObject } from "b-ui-lib";

type Props = {
  data: CaseDetailsData;
  containerStyle?: ViewStyle; // optional override for the container style
  relevantUsers: RelatedUsersList;
  caseDetailsLoading: boolean;
  caseDetailsError: string;
  metadata?: MetadataListDTO; // <-- new optional prop for metadata
  caseMetadata: any;
};

type DetailRowProps = {
  label: string;
  value: string;
  isBold?: boolean;
  isHtlm?: boolean;
};

/**
 * Helper function to resolve a user GUID (or array of GUIDs)
 * using the provided relevantUsers list. If a matching user is found,
 * it returns the user's value; otherwise, it falls back to the GUID.
 */
const resolveUser = (
  userGuid: string | string[],
  relevantUsers: RelatedUsersList
): string => {
  if (!userGuid) return "";
  if (Array.isArray(userGuid)) {
    // For an array of GUIDs (e.g. participants), map over them directly.
    const userNames = userGuid.map((guid) => {
      const found = relevantUsers.find((user) => user.id === guid);
      return found ? found.value : guid;
    });
    return userNames.join(", \n");
  } else {
    // For a single GUID, trim and look it up.
    const guid = userGuid.trim();
    const found = relevantUsers.find((user) => user.id === guid);
    return found ? found.value : guid;
  }
};

const DetailRow: React.FC<DetailRowProps> = ({
  label,
  value,
  isBold = false,
  isHtlm = false,
}) => {
  const { styles } = useThemeAwareObject(createStyles);
  const { width } = useWindowDimensions();

  return (
    <View style={styles.row}>
      <CustomText style={styles.label}>{label}</CustomText>

      {isHtlm ? (
        <RenderHtml
          contentWidth={width}
          source={{ html: value }}
          enableExperimentalMarginCollapsing={true}
          defaultTextProps={{
            style: styles.htmlTextStyle,
          }}
          baseStyle={{ flex: 5 }}
        />
      ) : (
        <CustomText style={[styles.value, isBold && styles.boldValue]}>
          {value}
        </CustomText>
      )}
    </View>
  );
};

const CaseDetailsTab = ({
  data,
  containerStyle,
  relevantUsers,
  caseDetailsLoading,
  caseDetailsError,
  caseMetadata,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  if (caseDetailsLoading) {
    return (
      <View
        style={[styles.container, { alignItems: "center", paddingTop: 60 }]}
      >
        <ActivityIndicator size="large" color={color.MESSAGE_FLAG} />
      </View>
    );
  }

  if (caseDetailsError) {
    return (
      <View
        style={[styles.container, { alignItems: "center", paddingTop: 60 }]}
      >
        <CustomText>{caseDetailsError}</CustomText>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, containerStyle]}>
      <CustomText style={styles.header}>Case Details</CustomText>

      <DetailRow label="Ref:" value={data?.ref} />

      <DetailRow label="Type:" value={data?.type} />

      <View style={styles.row}>
        <CustomText style={styles.label}>Status:</CustomText>
        <StatusLabel
          label={data.statusLabel}
          backgroundColor={data.statusBackgroundColor}
          style={{ container: { flex: 5 } }}
        />
      </View>

      <DetailRow label="Title:" value={data?.title} isBold />
      <DetailRow label="Description:" value={data?.description} isHtlm />

      {/* For user fields, we resolve the GUID(s) using the relevantUsers table */}
      <DetailRow
        label="Assign to:"
        value={resolveUser(data?.assignTo, relevantUsers)}
      />
      <DetailRow label="Project:" value={data?.project} />
      <DetailRow label="Priority:" value={data?.priority} />
      <DetailRow label="Due Date:" value={data?.dueDate} />
      <DetailRow
        label="Rep By:"
        value={resolveUser(data?.repBy, relevantUsers)}
        isBold
      />
      <DetailRow
        label="Created By:"
        value={resolveUser(data?.createdBy, relevantUsers)}
        isBold
      />
      <DetailRow
        label="Participants:"
        value={resolveUser(data?.participants, relevantUsers)}
        isBold
      />

      <View style={styles.row}>
        <CustomText style={styles.label}>Metadata:</CustomText>
        <View style={{ flex: 5 }}>
          {caseMetadata.length > 0 ? (
            caseMetadata.map((meta, index) => (
              <View key={meta.id} style={styles.metadataRow}>
                <CustomText style={styles.metadataLabel}>
                  {meta.label}:
                </CustomText>
                <CustomText style={styles.metadataValue}>
                  {meta.values}
                </CustomText>
              </View>
            ))
          ) : (
            <CustomText
              style={{ flex: 5, color: color.HALF_DIMMED, fontStyle: "italic" }}
            >
              No metadata available
            </CustomText>
          )}
        </View>
      </View>
    </ScrollView>
  );
};

export default CaseDetailsTab;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      padding: 16,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      flex: 1,
    },
    header: {
      fontSize: 16,
      fontWeight: "bold",
      marginBottom: 8,
    },
    row: {
      flexDirection: "row",
      marginBottom: 6,
    },
    label: {
      fontWeight: "600",
      marginRight: 6,
      fontSize: 12,
      flex: 2,
    },
    value: {
      flex: 5,
      fontSize: 12,
    },
    statusContainer: {
      backgroundColor: "#88CC29", // light green background
      borderRadius: 18,
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 6,
      gap: 5,
    },
    statusText: {
      color: color.TEXT_DEFAULT,
      fontWeight: "600",
    },
    boldValue: {
      fontWeight: "bold",
    },
    htmlTextStyle: {
      color: color.TEXT_DEFAULT,
    },
    metadataRow: {
      marginBottom: 12,
    },
    metadataLabel: {
      fontSize: 12,
      fontWeight: "700",
      marginBottom: 2,
      color: color.TEXT_DEFAULT,
    },
    metadataValue: {
      fontSize: 12,
      color: color.TEXT_DEFAULT,
    },
  });

  return { styles, color };
};
