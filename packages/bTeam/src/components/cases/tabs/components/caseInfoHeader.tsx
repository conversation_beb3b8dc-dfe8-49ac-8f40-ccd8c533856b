import {
  BenefitIconSet,
  CustomText,
  FONT_SIZES,
  formatDate,
  MessageDate,
  SPACING,
  Theme,
  useThemeAwareObject,
} from "b-ui-lib";
import React from "react";
import { type FC } from "react";
import { StyleSheet, View, type ViewStyle } from "react-native";

type Props = {
  subject: string;
  sentDate: string;
  refNumber: string;
  assignedTo: string;
  notificationIcon?: string;
  containerStyle?: ViewStyle;
};

const CaseInfoHeader: FC<Props> = ({
  subject,
  sentDate,
  notificationIcon,
  containerStyle,
  refNumber,
  assignedTo,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={containerStyle}>
      <CustomText style={styles.refTitle}>{refNumber}</CustomText>

      <View style={styles.container}>
        {notificationIcon && (
          <BenefitIconSet
            name={notificationIcon}
            size={24}
            color={color.NOTIFICATION_ICON}
          />
        )}

        <CustomText style={styles.subjectText}>{subject}</CustomText>

        <View style={styles.dateContainer}>
          <CustomText style={styles.dateText}>Added on</CustomText>
          <CustomText style={styles.date}> {formatDate(sentDate)}</CustomText>
        </View>
      </View>

      <CustomText style={styles.assigned} numberOfLines={1}>
        Assigned to:{" "}
        <CustomText style={{ fontWeight: "700" }}>{assignedTo}</CustomText>
      </CustomText>
    </View>
  );
};

export default CaseInfoHeader;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: "row",
      gap: SPACING.M,
      justifyContent: "space-between",
      alignItems: "center",
    },
    subjectText: {
      fontSize: 20,
      fontWeight: "bold",
      flex: 1,
    },
    refTitle: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.HALF_DIMMED,
    },
    dateContainer: {
      flex: 2,
      alignItems: "flex-end",
    },
    dateText: {
      fontSize: FONT_SIZES.TEN,
      color: color.TEXT_GREY_DARKER,
    },
    date: {
      fontSize: FONT_SIZES.TEN,
      color: color.TEXT_GREY,
    },
    assigned: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.HALF_DIMMED,
    },
  });

  return { styles, color };
};
