import React, { type RefObject } from "react";
import { FlatList, RefreshControl, StyleSheet, View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import type { BottomSheetMethods } from "@gorhom/bottom-sheet/lib/typescript/types";
import {
  type Theme,
  useThemeAwareObject,
  SPACING,
  CustomText,
  FONT_SIZES,
  FONT_WEIGHTS,
  CustomBottomSheet,
  ListEmptyComponent,
} from "b-ui-lib";
import { NotificationData } from "../../types/notification";
import Animated from "react-native-reanimated";

// Components
import NotificationItem from "./NotificationItem";
import NotificationListSkeleton from "./NotificationListSkeleton";

const AnimatedFlatList = Animated.createAnimatedComponent(
  FlatList<Notification>
);

type Props = {
  notifications: NotificationData[];
  handleRefreshList: () => void;
  isLoading: boolean;
  loadMoreNotifications: () => void;
  handleStarPress?: (payload: {
    notificationId: string;
    isStarred: boolean;
  }) => void;
  handleTapNotification?: (notificationId: string) => void;
  longTapToSelectNotification?: (notificationId: string) => void;
  tapToSelectAdditionalNotification?: (notificationId: string) => void;
  deselectNotification?: (notificationId: string) => void;
  cancelMultiSelection?: () => void;
  unSelectAllNotificationsAction?: () => void;
  isMultiSelectActive: boolean;
  selectedNotificationIds: string[];
  bottomSheetRef: RefObject<BottomSheetMethods>;
  errorMessage: string;
  handleMarkNotificationsAsReadUnRead: (read: boolean) => void;
  emptyMessage: string;
  // listFooterComponent: ReactElement;
};

const NotificationListScreen = ({
  notifications,
  handleRefreshList,
  isLoading,
  loadMoreNotifications,
  handleStarPress,
  handleTapNotification,
  longTapToSelectNotification,
  tapToSelectAdditionalNotification,
  deselectNotification,
  cancelMultiSelection,
  unSelectAllNotificationsAction,
  isMultiSelectActive,
  selectedNotificationIds,
  bottomSheetRef,
  errorMessage,
  handleMarkNotificationsAsReadUnRead,
  emptyMessage,
  listStyle,
  onLayout,
  onScroll,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  const handleLongTapToSelectNotification = (notificationId: string) => {
    bottomSheetRef?.current?.snapToIndex(0);
    longTapToSelectNotification(notificationId);
  };

  const handleBottomSheetBackPress = () => {
    if (isMultiSelectActive) {
      cancelMultiSelection();
      return true;
    }

    return false;
  };

  const handleSheetChanges = (index: number) => {
    if (index === -1) {
      cancelMultiSelection();
    }
  };

  const BOTTOM_SHEET_BUTTONS = [
    {
      title: "Mark as Read",
      onPress: () => handleMarkNotificationsAsReadUnRead(true),
      style: styles.bottomSheetText,
    },
    {
      title: "Mark as Unread",
      onPress: () => handleMarkNotificationsAsReadUnRead(false),
      style: styles.bottomSheetText,
    },
    {
      title: "Clear All",
      onPress: unSelectAllNotificationsAction,
      style: styles.bottomSheetText,
    },
  ];

  if (errorMessage) {
    return (
      <View style={styles.container}>
        <CustomText style={styles.errorMessage}>{errorMessage}</CustomText>
      </View>
    );
  }

  if (isLoading) {
    return <NotificationListSkeleton />;
  }

  return (
    <GestureHandlerRootView>
      <View style={styles.container}>
        <AnimatedFlatList
          onLayout={onLayout}
          onScroll={onScroll}
          style={listStyle}
          data={notifications}
          onEndReached={loadMoreNotifications}
          ListEmptyComponent={<ListEmptyComponent emptyText={emptyMessage} />}
          renderItem={({ item }) => (
            <NotificationItem
              notification={item}
              isChecked={
                !!selectedNotificationIds?.find(
                  (selectedNotificationId) => selectedNotificationId === item.id
                )
              }
              handleStarPress={handleStarPress}
              handleTapNotification={handleTapNotification}
              handleLongTapToSelectNotification={
                handleLongTapToSelectNotification
              }
              handleDeselectMessage={deselectNotification}
              handleTapToSelectAdditionalEmail={
                tapToSelectAdditionalNotification
              }
              isMultiSelectActive={isMultiSelectActive}
            />
          )}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={() => handleRefreshList()}
              tintColor={color.BLACK}
            />
          }
        />
      </View>

      <CustomBottomSheet
        bottomSheetRef={bottomSheetRef}
        handleBackPress={handleBottomSheetBackPress}
        handleSheetChanges={handleSheetChanges}
      >
        <View style={styles.bottomSheetBody}>
          {BOTTOM_SHEET_BUTTONS.map((button, index) => {
            const { title, ...restProps } = button || {};

            return (
              <CustomText key={`${index}-${title}`} {...restProps}>
                {button.title}
              </CustomText>
            );
          })}
        </View>
      </CustomBottomSheet>
    </GestureHandlerRootView>
  );
};

export default NotificationListScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
    errorMessage: {
      color: color.ERROR,
      alignSelf: "center",
      paddingTop: SPACING.L,
    },
    listFooterComponent: {
      height: 50,
    },
    composeIcon: {
      backgroundColor: color.BRAND_DEFAULT,
      padding: SPACING.M,
      borderRadius: 30,
      position: "absolute",
      bottom: 30,
      right: "5%",
    },
    bottomSheetBody: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      backgroundColor: color.NOTIFICATION_BOTTOM_SHEET_TEXT_BACKGROUND_COLOR,
    },
    bottomSheetText: {
      fontSize: FONT_SIZES.SIXTEEN,
      fontWeight: FONT_WEIGHTS.FIVE_HUNDRED,
      color: color.NOTIFICATION_BOTTOM_SHEET_TEXT_COLOR,
      backgroundColor: color.NOTIFICATION_BOTTOM_SHEET_TEXT_BACKGROUND_COLOR,
      paddingVertical: SPACING.FOURTEEN,
      paddingHorizontal: SPACING.M,
    },
  });

  return { styles, color };
};
