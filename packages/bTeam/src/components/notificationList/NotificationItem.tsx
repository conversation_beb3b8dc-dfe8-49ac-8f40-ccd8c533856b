import React, { type FC } from "react";
import { Pressable, StyleSheet, View } from "react-native";
import {
  useThemeAwareObject,
  type Theme,
  FONT_SIZES,
  FONT_WEIGHTS,
  IconTextButton,
  SPACING,
  IconButton,
  Checkbox,
  CustomText,
  Avatar,
  BenefitIconSet,
  formatDate,
} from "b-ui-lib";
import { NotificationData } from "../../types/notification";
import { ICON_POSITIONS } from "b-ui-lib/src/components/IconTextButton";
import { NOTIFICATION_ENTITY_ICON } from "../../constants/notificationsEntityIds";

type Props = {
  notification: NotificationData;
  isChecked: boolean;
  handleStarPress?: (payload: {notificationId: string, isStarred: boolean}) => void;
  handleTapNotification?: (notificationId: string) => void;
  handleLongTapToSelectNotification?: (notificationId: string) => void;
  handleDeselectMessage?: (notificationId: string) => void;
  handleTapToSelectAdditionalEmail?: (notificationId: string) => void;
  isMultiSelectActive?: boolean;
};

export const NotificationItem: FC<Props> = ({
  notification,
  isChecked,
  handleStarPress,
  handleTapNotification,
  handleLongTapToSelectNotification,
  handleDeselectMessage,
  handleTapToSelectAdditionalEmail,
  isMultiSelectActive,
}) => {
  const {
    title,
    avatarName,
    username,
    description,
    dateTime,
    attachmentsCount,
    isViewed,
    isStarred,
    entityId,
    id,
  } = notification || {};
  const { styles, color } = useThemeAwareObject((theme) =>
    createStyles(theme, isChecked, isViewed)
  );

  const handleTapMessageCheckbox = (messageId: string) => {
    if (isChecked) {
      return handleDeselectMessage(messageId);
    }

    return handleTapToSelectAdditionalEmail(messageId);
  };

  return (
    <Pressable
      onPress={() => handleTapNotification(notification.id)}
      onLongPress={() => handleLongTapToSelectNotification(notification.id)}
      style={styles.container}
    >
      <View style={styles.notificationInfo}>
        <View style={styles.titleContainer}>
          <BenefitIconSet
            name={NOTIFICATION_ENTITY_ICON[entityId]}
            color={
              isViewed
                ? color.MESSAGE_CATEGORY_ATTACHMENTS
                : color.NOTIFICATION_FOLDER_ICON
            }
            size={22}
          />

          <CustomText style={styles.title}>{title}</CustomText>
        </View>

        <View style={styles.usernameContainer}>
          <Avatar
            name={avatarName}
            isSmall
            style={{ container: styles.avatar }}
          />

          <View style={styles.usernameBody}>
            <CustomText style={styles.username}>{`#${username}#`}</CustomText>
            <CustomText
              style={styles.repliedAt}
            >{` Replied to all at ${formatDate(dateTime)}`}</CustomText>
          </View>
        </View>

        <View style={styles.bodyContainer}>
          <View style={styles.hiddenView} />

          <CustomText style={styles.description}>{description}</CustomText>
        </View>
      </View>

      <View style={styles.notificationActions}>
        <IconButton
          name={isStarred ? "star-filled" : "star"}
          size={24}
          color={color.TEXT_DISABLED_2}
          onPress={() =>
            handleStarPress &&
            handleStarPress({
              notificationId: notification.id,
              isStarred: !isStarred,
            })
          }
        />

        <IconTextButton
          iconName={"paperclip"}
          iconSize={16}
          iconColor={color.MESSAGE_ITEM_ICON}
          title={attachmentsCount?.toString()}
          textStyle={{ color: color.NOTIFICATION_PAPERCLIP_TEXT }}
          iconPosition={ICON_POSITIONS.right}
        />
      </View>

      {isMultiSelectActive && (
        <View style={styles.checkbox}>
          <Checkbox
            isChecked={isChecked}
            onPress={() => handleTapMessageCheckbox(notification.id)}
          />
        </View>
      )}
    </Pressable>
  );
};

export default NotificationItem;

const createStyles = (
  { color }: Theme,
  isChecked: boolean,
  isViewed: boolean
) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: isChecked
        ? color.MESSAGE_ITEM__SELECTED_BACKGROUND
        : color.MESSAGE_ITEM__BACKGROUND,
      flex: 1,
      flexDirection: "row",
      gap: SPACING.SIX,
      padding: SPACING.M,
      borderTopWidth: 1,
      borderTopColor: color.PRESSABLE_HOVER,
    },
    notificationInfo: {
      flex: 1,
    },
    notificationActions: {
      justifyContent: "space-between",
      alignItems: "center",
    },
    titleContainer: {
      flexDirection: "row",
      gap: SPACING.SIX,
      alignItems: "center",
      color: color.TEXT_SEARCH_INVERTED,
      justifyContent: "flex-start",
    },
    title: {
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: isViewed ? FONT_WEIGHTS.NORMAL : FONT_WEIGHTS.BOLD,
      color: isViewed
        ? color.NOTIFICATION_TITLE_BLUR
        : color.TEXT_SEARCH_INVERTED,
    },
    usernameContainer: {
      paddingVertical: SPACING.XXS,
      flexDirection: "row",
      gap: SPACING.SIX,
      alignItems: "center",
      justifyContent: "flex-start",
    },
    avatar: {
      marginHorizontal: 4,
    },
    usernameBody: {
      flexDirection: "row",
    },
    username: {
      fontSize: FONT_SIZES.TWELVE,
      fontWeight: FONT_WEIGHTS.BOLD,
      color: color.NOTIFICATION_USERNAME,
    },
    repliedAt: {
      fontSize: FONT_SIZES.TEN,
      color: color.TEXT_DISABLED_2,
    },
    bodyContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: SPACING.SIX,
    },
    hiddenView: {
      width: 21,
      height: 21,
    },
    description: {
      fontSize: FONT_SIZES.TWELVE,
      color: isViewed
        ? color.MESSAGE_ITEM__DESC
        : color.NOTIFICATION_NOT_VIEWED_BODY,
    },
    checkbox: {
      justifyContent: "center",
    },
  });

  return { styles, color };
};
