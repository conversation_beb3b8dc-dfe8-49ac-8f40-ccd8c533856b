import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  NativeSyntheticEvent,
  ScrollView,
  StyleSheet,
  TextInputChangeEventData,
  View,
} from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { BottomSheetModal } from "@gorhom/bottom-sheet";
import { NavigationProp, ParamListBase } from "@react-navigation/native";
import {
  type AvatarEmail,
  Button,
  CustomText,
  FONT_SIZES,
  SPACING,
  Theme,
  useThemeAwareObject,
  AdvancedSearchButtons,
  SearchCriteriaField,
  Modal,
} from "b-ui-lib";
import {
  NOTIFICATIONS_SEARCH_FIELD_NAMES,
  NOTIFICATIONS_SEARCH_FIELD_NAMES_OPTIONS,
} from "../../constants/notificationsSearchFields";
import { NotificationsSearchFields } from "../../containers/NotificationsSearchFiltersHOC";
import { ParticipantUser } from "../../types/participantUser";
import SelectRecipientsBottomSheet from "../toBeMovedToUILib/SelectRecipientsBottomSheet";
import SearchFilterInputOptions from "../toBeMovedToUILib/SearchFilterInputOptions";
import DateTimePicker, {
  DateType,
  useDefaultStyles,
} from "react-native-ui-datepicker";
import { dateDefaultValueOrNull } from "../../helpers/checkIfStringValueExists";

type Props = {
  searchFilters: NotificationsSearchFields;
  setSearchFilters: (payload: NotificationsSearchFields) => void;
  searchFiltersCounter: number;
  fetchGridNotifications: () => void;
  clearNotificationsSearchFields: () => void;
  setIsQuickSearchEnabled: (payload: boolean) => void;
  navigation: NavigationProp<ParamListBase>;
  recipientsEmails: AvatarEmail[];
  participantUsersById: ParticipantUser[];
  searchSuggestions: string[];
};

const NotificationsSearchFiltersScreen: React.FC = ({
  searchFilters,
  setSearchFilters,
  searchFiltersCounter,
  fetchGridNotifications,
  clearNotificationsSearchFields,
  setIsQuickSearchEnabled,
  navigation,
  recipientsEmails,
  participantUsersById,
  searchSuggestions,
}: Props) => {
  const [selectedFieldName, setSelectedFieldName] = useState<string>("");
  const [temporaryFilters, setTemporaryFilters] =
    useState<NotificationsSearchFields>(searchFilters);
  const [isDateModalVisible, setIsDateModalVisible] = useState<boolean>(false);
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const defaultStyles = useDefaultStyles();

  const { styles, color } = useThemeAwareObject(createStyles);

  const handlePresentModalPress = () => bottomSheetRef.current?.expand();

  const handleDismiss = () => setSelectedFieldName("");

  const closeBottomSheet = () => bottomSheetRef.current?.close();

  const handleBackPress = () => {
    if (selectedFieldName) {
      closeBottomSheet();
      return true;
    }

    return false;
  };

  useEffect(() => {
    if (JSON.stringify(searchFilters) !== JSON.stringify(temporaryFilters)) {
      setTemporaryFilters(searchFilters);
    }
  }, [searchFilters]);

  const handleFieldPress = (fieldName: string) => {
    setSelectedFieldName(fieldName);
    handlePresentModalPress();
  };

  const handleOptionPress = (option: string) => {
    setTemporaryFilters({ ...temporaryFilters, [selectedFieldName]: option });
    closeBottomSheet();
  };

  const handleApplyCriteriaPress = () => {
    if (JSON.stringify(searchFilters) !== JSON.stringify(temporaryFilters)) {
      setIsQuickSearchEnabled(false);
    }

    setSearchFilters(temporaryFilters);
    fetchGridNotifications();
    navigation.goBack();
  };

  const findOptionName = (field: string, optionValue: string) =>
    NOTIFICATIONS_SEARCH_FIELD_NAMES_OPTIONS[field].find(
      (option) => option.value === optionValue
    )?.name;

  const FIELDS = [
    {
      name: NOTIFICATIONS_SEARCH_FIELD_NAMES.searchText,
      placeholder: "Add keywords",
      label: "Keywords",
      iconName: "plus",
      iconColor: color.BLUE_DEFAULT,
      onChange: (e: NativeSyntheticEvent<TextInputChangeEventData>) => {
        setTemporaryFilters({
          ...temporaryFilters,
          [NOTIFICATIONS_SEARCH_FIELD_NAMES.searchText]: e?.nativeEvent?.text,
        });
      },
      value: temporaryFilters?.searchText,
      component: SearchCriteriaField,
      suggestions: !temporaryFilters?.searchText
        ? searchSuggestions.slice(0, 3)
        : [],
      handleSuggestionPress: (suggestion: string) => {
        setTemporaryFilters({
          ...temporaryFilters,
          [NOTIFICATIONS_SEARCH_FIELD_NAMES.searchText]: suggestion,
        });
      },
    },
    {
      name: NOTIFICATIONS_SEARCH_FIELD_NAMES.createDateFrom,
      placeholder: "Not Set",
      label: "Creation Date",
      iconName: "arrow-right",
      iconColor: color.BLUE_DEFAULT,
      editable: false,
      onPress: () => {
        setSelectedFieldName(NOTIFICATIONS_SEARCH_FIELD_NAMES.createDateFrom);
        setIsDateModalVisible(true);
      },
      value:
        temporaryFilters?.createDateFrom && temporaryFilters?.createDateTo
          ? `${dateDefaultValueOrNull(
              temporaryFilters?.createDateFrom
            )} - ${dateDefaultValueOrNull(temporaryFilters?.createDateTo)}`
          : "",
      component: SearchCriteriaField,
      selectedRecipientEmailId: null,
      handleSelectRecipientEmail: null,
      bottomSheetChildren: null,
      bottomSheetTitleBoldText: "",
      bottomSheetTitleNormalText: "",
      dateTimeValue: {
        startDate: temporaryFilters?.createDateFrom,
        endDate: temporaryFilters?.createDateTo,
      },
      handleRangeChange: ({
        startDate,
        endDate,
      }: {
        startDate?: DateType;
        endDate?: DateType;
      }) => {
        setTemporaryFilters({
          ...temporaryFilters,
          [NOTIFICATIONS_SEARCH_FIELD_NAMES.createDateFrom]: startDate,
          [NOTIFICATIONS_SEARCH_FIELD_NAMES.createDateTo]: endDate,
        });
      },
    },
    {
      name: NOTIFICATIONS_SEARCH_FIELD_NAMES.from,
      placeholder: "Not Set",
      label: "From",
      iconName: "plus",
      iconColor: color.BLUE_DEFAULT,
      editable: false,
      onPress: () => handleFieldPress(NOTIFICATIONS_SEARCH_FIELD_NAMES.from),
      value: temporaryFilters?.from
        ?.map((id) => participantUsersById?.[id]?.username)
        ?.join(", "),
      component: SearchCriteriaField,
      selectedRecipientEmailId: temporaryFilters?.from,
      handleSelectRecipientEmail: (emailId: string) =>
        setTemporaryFilters({
          ...temporaryFilters,
          [NOTIFICATIONS_SEARCH_FIELD_NAMES.from]:
            temporaryFilters?.from?.includes(emailId) ? [] : [emailId],
        }),
      bottomSheetChildren: {},
      bottomSheetTitleBoldText: "Select",
      bottomSheetTitleNormalText: "From",
      dateTimeValue: null,
    },
    {
      name: NOTIFICATIONS_SEARCH_FIELD_NAMES.participants,
      placeholder: "Not Set",
      label: "Participants",
      iconName: "plus",
      iconColor: color.BLUE_DEFAULT,
      editable: false,
      onPress: () =>
        handleFieldPress(NOTIFICATIONS_SEARCH_FIELD_NAMES.participants),
      value: temporaryFilters.participants
        ?.map((id) => participantUsersById?.[id]?.username)
        ?.join(", "),
      component: SearchCriteriaField,
      selectedRecipientEmailId: temporaryFilters?.participants,
      handleSelectRecipientEmail: (emailId: string) =>
        setTemporaryFilters({
          ...temporaryFilters,
          [NOTIFICATIONS_SEARCH_FIELD_NAMES.participants]:
            temporaryFilters.participants?.includes(emailId)
              ? temporaryFilters?.participants?.filter((id) => id !== emailId)
              : [...(temporaryFilters?.participants || []), emailId],
        }),
      bottomSheetChildren: {},
      bottomSheetTitleBoldText: "Select",
      bottomSheetTitleNormalText: "Participants",
      dateTimeValue: null,
    },
    {
      name: NOTIFICATIONS_SEARCH_FIELD_NAMES.notifiedUsers,
      placeholder: "Not Set",
      label: "Notified Users",
      iconName: "plus",
      iconColor: color.BLUE_DEFAULT,
      editable: false,
      onPress: () =>
        handleFieldPress(NOTIFICATIONS_SEARCH_FIELD_NAMES.notifiedUsers),
      value: temporaryFilters.notifiedUsers
        ?.map((id) => participantUsersById?.[id]?.username)
        ?.join(", "),
      component: SearchCriteriaField,
      selectedRecipientEmailId: temporaryFilters?.notifiedUsers,
      handleSelectRecipientEmail: (emailId: string) =>
        setTemporaryFilters({
          ...temporaryFilters,
          [selectedFieldName]: temporaryFilters.notifiedUsers?.includes(emailId)
            ? temporaryFilters?.notifiedUsers?.filter((id) => id !== emailId)
            : [...(temporaryFilters?.notifiedUsers || []), emailId],
        }),
      bottomSheetChildren: {},
      bottomSheetTitleBoldText: "Select",
      bottomSheetTitleNormalText: "Notified Users",
      dateTimeValue: null,
    },
    {
      name: NOTIFICATIONS_SEARCH_FIELD_NAMES.readStatus,
      placeholder: "Not Set",
      label: "Read Status",
      iconName: "arrow-right",
      iconColor: color.BLUE_DEFAULT,
      editable: false,
      onPress: () =>
        handleFieldPress(NOTIFICATIONS_SEARCH_FIELD_NAMES.readStatus),
      value: findOptionName(
        NOTIFICATIONS_SEARCH_FIELD_NAMES.readStatus,
        temporaryFilters?.readStatus
      ),
      component: SearchCriteriaField,
      selectedRecipientEmailId: null,
      handleSelectRecipientEmail: null,
      bottomSheetChildren: (
        <SearchFilterInputOptions
          bottomSheetRef={bottomSheetRef}
          handleBackPress={handleBackPress}
          handleBackdropPress={handleDismiss}
          options={
            NOTIFICATIONS_SEARCH_FIELD_NAMES_OPTIONS?.[selectedFieldName]
          }
          handleOptionPress={(optionValue: string) =>
            handleOptionPress(optionValue)
          }
        />
      ),
      bottomSheetTitleBoldText: "",
      bottomSheetTitleNormalText: "",
      dateTimeValue: null,
    },
    {
      name: NOTIFICATIONS_SEARCH_FIELD_NAMES.notified,
      placeholder: "Not Set",
      label: "Notified",
      iconName: "arrow-right",
      iconColor: color.BLUE_DEFAULT,
      editable: false,
      onPress: () =>
        handleFieldPress(NOTIFICATIONS_SEARCH_FIELD_NAMES.notified),
      value: findOptionName(
        NOTIFICATIONS_SEARCH_FIELD_NAMES.notified,
        temporaryFilters?.notified
      ),
      component: SearchCriteriaField,
      selectedRecipientEmailId: null,
      handleSelectRecipientEmail: null,
      bottomSheetChildren: (
        <SearchFilterInputOptions
          bottomSheetRef={bottomSheetRef}
          handleBackPress={handleBackPress}
          handleBackdropPress={handleDismiss}
          options={
            NOTIFICATIONS_SEARCH_FIELD_NAMES_OPTIONS?.[selectedFieldName]
          }
          handleOptionPress={(optionValue: string) =>
            handleOptionPress(optionValue)
          }
        />
      ),
      bottomSheetTitleBoldText: "",
      bottomSheetTitleNormalText: "",
      dateTimeValue: null,
    },
    {
      name: NOTIFICATIONS_SEARCH_FIELD_NAMES.requestForReply,
      placeholder: "Not Set",
      label: "Request For Reply",
      iconName: "arrow-right",
      iconColor: color.BLUE_DEFAULT,
      editable: false,
      onPress: () =>
        handleFieldPress(NOTIFICATIONS_SEARCH_FIELD_NAMES.requestForReply),
      value: findOptionName(
        NOTIFICATIONS_SEARCH_FIELD_NAMES.requestForReply,
        temporaryFilters?.requestForReply
      ),
      component: SearchCriteriaField,
      selectedRecipientEmailId: null,
      handleSelectRecipientEmail: null,
      bottomSheetChildren: (
        <SearchFilterInputOptions
          bottomSheetRef={bottomSheetRef}
          handleBackPress={handleBackPress}
          handleBackdropPress={handleDismiss}
          options={
            NOTIFICATIONS_SEARCH_FIELD_NAMES_OPTIONS?.[selectedFieldName]
          }
          handleOptionPress={(optionValue: string) =>
            handleOptionPress(optionValue)
          }
        />
      ),
      bottomSheetTitleBoldText: "",
      bottomSheetTitleNormalText: "",
      dateTimeValue: null,
    },
    {
      name: NOTIFICATIONS_SEARCH_FIELD_NAMES.isStarred,
      placeholder: "Not Set",
      label: "Starred",
      iconName: "arrow-right",
      iconColor: color.BLUE_DEFAULT,
      editable: false,
      onPress: () =>
        handleFieldPress(NOTIFICATIONS_SEARCH_FIELD_NAMES.isStarred),
      value: findOptionName(
        NOTIFICATIONS_SEARCH_FIELD_NAMES.isStarred,
        temporaryFilters?.isStarred
      ),
      component: SearchCriteriaField,
      selectedRecipientEmailId: null,
      handleSelectRecipientEmail: null,
      bottomSheetChildren: (
        <SearchFilterInputOptions
          bottomSheetRef={bottomSheetRef}
          handleBackPress={handleBackPress}
          handleBackdropPress={handleDismiss}
          options={
            NOTIFICATIONS_SEARCH_FIELD_NAMES_OPTIONS?.[selectedFieldName]
          }
          handleOptionPress={(optionValue: string) =>
            handleOptionPress(optionValue)
          }
        />
      ),
      bottomSheetTitleBoldText: "",
      bottomSheetTitleNormalText: "",
      dateTimeValue: null,
    },
  ];

  const selectedField = FIELDS?.find(
    (field) => field.name === selectedFieldName
  );

  return (
    <GestureHandlerRootView>
      <View style={styles.container}>
        <AdvancedSearchButtons
          handleClearAll={clearNotificationsSearchFields}
          isClearAllButtonVisible={searchFiltersCounter > 0}
          isSearchCriteriaButtonActive
          style={{ buttonsContainer: styles.headerButtons }}
        />

        <View style={styles.inputsContainer}>
          <CustomText style={styles.advancedText}>Advanced Criteria</CustomText>

          <ScrollView contentContainerStyle={styles.scrollViewContentContainer}>
            {FIELDS?.map((field, index) => {
              const {
                name,
                component: Component,
                selectedRecipientEmailId,
                handleSelectRecipientEmail,
                bottomSheetChildren,
                dateTimeValue,
                bottomSheetTitleBoldText,
                bottomSheetTitleNormalText,
                ...restProps
              } = field || {};
              return <Component key={`${name}  ${index}`} {...restProps} />;
            })}
          </ScrollView>
        </View>

        <View style={styles.applyButtonContainer}>
          <Button
            title="Apply Criteria"
            textStyle={styles.applyButtonText}
            onPress={handleApplyCriteriaPress}
          />
        </View>
      </View>

      <SelectRecipientsBottomSheet
        bottomSheetRef={bottomSheetRef}
        handleBackPress={handleBackPress}
        handleBackdropPress={handleDismiss}
        recipientsEmails={recipientsEmails}
        handleAddButton={closeBottomSheet}
        handleCloseBottomSheet={closeBottomSheet}
        titleBoldText={selectedField?.bottomSheetTitleBoldText}
        titleNormalText={selectedField?.bottomSheetTitleNormalText}
        fieldToFilterSearch="recipient"
        selectedRecipientEmailId={selectedField?.selectedRecipientEmailId}
        handleSelectRecipientEmail={selectedField?.handleSelectRecipientEmail}
      >
        {selectedField?.bottomSheetChildren}
      </SelectRecipientsBottomSheet>

      <Modal
        isVisible={isDateModalVisible}
        handleClose={() => setIsDateModalVisible(false)}
      >
        <DateTimePicker
          mode="range"
          startDate={selectedField?.dateTimeValue?.startDate}
          endDate={selectedField?.dateTimeValue?.endDate}
          onChange={
            selectedField?.handleRangeChange && selectedField.handleRangeChange
          }
          styles={{
            ...defaultStyles,
            selected: styles.datePickerBackgroundColor,
            range_middle: styles.datePickerBackgroundColor,
            today: styles.datePickerToday,
            range_start_label: styles.datePickerColor,
            range_end_label: styles.datePickerColor,
            range_middle_label: styles.datePickerColor,
            selected_label: styles.datePickerColor,
          }}
        />
      </Modal>
    </GestureHandlerRootView>
  );
};

export default NotificationsSearchFiltersScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    avoidingViewContainer: {
      flex: 1,
      justifyContent: "center",
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
    },
    container: {
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      flex: 1,
    },
    headerButtons: {
      padding: SPACING.M,
    },
    inputsContainer: {
      flex: 1,
      paddingTop: SPACING.XL,
      paddingHorizontal: SPACING.M,
    },
    advancedText: {
      color: color.TEXT_SEARCH_INVERTED,
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: "bold",
      alignSelf: "flex-start",
      marginBottom: SPACING.M,
    },
    scrollViewContentContainer: {
      gap: SPACING.SIX,
    },
    applyButtonContainer: {
      borderTopWidth: 1,
      borderTopColor: color.SEARCH_FIELD_BORDER,
      padding: SPACING.M,
    },
    applyButtonText: {
      fontWeight: "500",
    },
    bottomSheetStyle: {
      backgroundColor: color.BACKGROUND,
    },
    bottomSheetIndicatorStyle: {
      backgroundColor: color.MESSAGE_FLAG,
    },
    datePickerBackgroundColor: {
      backgroundColor: color.BRAND_DEFAULT,
    },
    datePickerColor: {
      color: color.BLACK,
    },
    datePickerToday: {
      borderWidth: 1,
      borderColor: color.BRAND_DEFAULT,
      borderRadius: 100,
    },
  });

  return { styles, color };
};
