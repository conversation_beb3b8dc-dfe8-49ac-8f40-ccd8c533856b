import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import {
  fetchGridNotifications,
  fetchGridNotificationsFailed,
  fetchGridNotificationsSuccess,
} from "../slices/notificationsSlice";
import { NOTIFICATIONS_INITIAL_PAGE_SIZE } from "../containers/NotificationListHOC";
import { storeNotifications } from "../slices/commentsSlice";
import {
  checkArrayAndJoinStringValues,
  dateDefaultValueOrNull,
  stringDefaultValueOrNull,
} from "../helpers/checkIfStringValueExists";
import { setFilteredNotificationIds } from "../slices/generalNotificationsSlice";

export const getGridNotifications = (sources) => {
  const state$ = sources.STATE;
  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const domainBaseUrl$ = state$.map((state) => state?.persist?.bTeamAuth?.domainBaseUrl);
  const searchFilters$ = state$.map(
    (state) => state?.root?.bTeamGeneralNotificationSlice?.searchFilters
  );
  const actionPayload$ = sources.ACTION.filter(
    (action) => action.type === fetchGridNotifications.type
  );

  const request$ = sources.ACTION.filter(
    (action) => action.type === fetchGridNotifications.type
  )
    .compose(sampleCombine(token$, searchFilters$, domainBaseUrl$))
    .map(([action, token, searchFilters, domainBaseUrl]) => {
      const { offset } = action.payload || {};

      const {
        searchText,
        createDateFrom,
        createDateTo,
        from,
        participants,
        notifiedUsers,
        readStatus,
        notified,
        requestForReply,
        isStarred,
      } = searchFilters || {};

      const JSON_FILTER_CRITERIA_DEFAULT_VALUE = {
        Type: 2,
        Fields: [
          {
            FieldName: "SearchText",
            FieldValue: stringDefaultValueOrNull(searchText),
            FieldOrder: 0,
          },
          {
            FieldName: "CreateDateFrom",
            FieldValue: dateDefaultValueOrNull(createDateFrom, "YYYY-MM-DD"),
            FieldOrder: 5,
          },
          {
            FieldName: "CreateDateTo",
            FieldValue: dateDefaultValueOrNull(createDateTo, "YYYY-MM-DD"),
            FieldOrder: 6,
          },
          {
            FieldName: "ReceivedFromUsers",
            FieldValue: checkArrayAndJoinStringValues(from),
            FieldOrder: 1,
          },
          {
            FieldName: "Participants",
            FieldValue: checkArrayAndJoinStringValues(participants),
            FieldOrder: 2,
          },
          {
            FieldName: "Notified",
            FieldValue: checkArrayAndJoinStringValues(notifiedUsers),
            FieldOrder: 3,
          },
          {
            FieldName: "ReadUnread",
            FieldValue: stringDefaultValueOrNull(readStatus),
            FieldOrder: 4,
          },
          {
            FieldName: "NotifiedStatus",
            FieldValue: stringDefaultValueOrNull(notified),
            FieldOrder: 9,
          },
          {
            FieldName: "CommentRequestForReplyStatus",
            FieldValue: stringDefaultValueOrNull(requestForReply),
            FieldOrder: 7,
          },
          {
            FieldName: "IsStarred",
            FieldValue: stringDefaultValueOrNull(isStarred),
            FieldOrder: 8,
          },
        ],
      };

      const queryParameters = new URLSearchParams();

      queryParameters.append("maxLength", "200");
      queryParameters.append("$inlinecount", "allpages");
      queryParameters.append(
        "$top",
        NOTIFICATIONS_INITIAL_PAGE_SIZE.toString()
      );
      queryParameters.append("$skip", (offset || 0).toString());
      queryParameters.append("$orderby", "CMM_DateTime desc");
      queryParameters.append(
        "jsonFilterCriteria",
        JSON.stringify(JSON_FILTER_CRITERIA_DEFAULT_VALUE)
      );

      return {
        url: `${domainBaseUrl}/odt/CMM_CommentGridODTs?${queryParameters.toString()}`,
        category: "getGridNotifications",
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
    });

  const response$ = sources.HTTP.select("getGridNotifications")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200);

  const action1$ = response$
    .compose(sampleCombine(actionPayload$))
    .map(([response, actionPayload]) => {
      const responseBody = response?.body;
      const { offset } = actionPayload?.payload || {};

      return fetchGridNotificationsSuccess({
        responseBody,
        offset,
      });
    });

  const action2$ = response$
    .compose(sampleCombine(actionPayload$))
    .map(([response, actionPayload]) => {
      const responseBody = response?.body;

      return storeNotifications({
        responseBody,
      });
    });

  const action3$ = response$
    .compose(sampleCombine(actionPayload$))
    .map(([response, actionPayload]) => {
      return setFilteredNotificationIds({
        responseBody: response?.body,
        offset: actionPayload?.payload?.offset,
      });
    });

  return {
    ACTION: xs.merge(action1$, action2$, action3$),
    HTTP: request$,
  };
};

export const getGridNotificationsFailed = (sources) => {
  const response$ = sources.HTTP.select("getGridNotifications")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = xs
    .combine(response$)
    .map((arr) => fetchGridNotificationsFailed(arr));
  return {
    ACTION: action$,
  };
};
