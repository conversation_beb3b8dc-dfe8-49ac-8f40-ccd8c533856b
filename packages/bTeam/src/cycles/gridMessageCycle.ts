import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import { GridMessageResponseDTO } from "../types/DTOs/GridMessageResponseDTO";
import { ResponseDTO } from "../types/DTOs/ResponseDTO";
import {
  fetchGridMessageFailed,
  fetchGridMessages,
  fetchGridMessageSuccess,
  getSearchFolderCriteria,
} from "../slices/gridMessageSlice";
import { checkIfStringValueExists } from "../helpers/checkIfStringValueExists";
import { hasSearchFilters } from "../constants/searchFieldsMessage";
import {
  setAppliedSearchFiltersCount,
  setFilteredIds,
} from "../slices/searchFiltersSlice";
import { SEARCH_ENTITIES } from "../constants/searchEntities";
import { escapeQuotedContent } from "../helpers/validateSearchText";
import { isSearchFolderSavedCriteria } from "../helpers/searchFolderSavedCriteria";
import { SEARCH_RESULTS_FOLDER_ID } from "../constants/searchResultsFolder";

export const getGridMessages = (sources) => {
  const state$ = sources.STATE;
  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const domainBaseUrl$ = state$.map(
    (state) => state?.persist?.bTeamAuth?.domainBaseUrl
  );
  const selectedFolderId$ = state$.map(
    (state) => state?.persist?.gridMessageSlice?.selectedFolderId
  );
  const foldersById$ = state$.map(
    (state) => state?.persist?.gridMessageSlice?.folders?.byId
  );
  const searchFilters$ = state$.map(
    (state) =>
      state?.root?.bTeamSearchFiltersSlice?.[SEARCH_ENTITIES.messages]
        ?.searchFilters
  );

  const actionPayload$ = sources.ACTION.filter(
    (action) => action.type === fetchGridMessages.type
  );

  const request$ = sources.ACTION.filter(
    (action) => action.type === fetchGridMessages.type
  )
    .compose(
      sampleCombine(
        token$,
        selectedFolderId$,
        searchFilters$,
        domainBaseUrl$,
        foldersById$
      )
    )
    .map(
      ([
        action,
        token,
        selectedFolderId,
        searchFilters,
        domainBaseUrl,
        foldersById,
      ]) => {
        const { pageSize, offset } = action.payload || {};

        const {
          inOut,
          searchText,
          searchIn,
          from,
          to,
          datePeriod,
          flagged,
          hasAttachments,
          readUnread,
        } = searchFilters || {};

        // Proceed only if searchFilters are not null, and it's not a saved criteria folder (no filters should be added in that case)
        // Or selected folder is the temporary search results folder
        const hasFilters =
          (hasSearchFilters(searchFilters) &&
            !isSearchFolderSavedCriteria(foldersById?.[selectedFolderId])) ||
          selectedFolderId === SEARCH_RESULTS_FOLDER_ID;

        let jsonFilterCriteria = {};
        let newSelectedFolderId =
          selectedFolderId !== SEARCH_RESULTS_FOLDER_ID
            ? selectedFolderId
            : foldersById?.[selectedFolderId]?.searchResultsFolderIds?.join(
                ","
              );

        if (hasFilters) {
          jsonFilterCriteria = {
            Type: 2,
            Fields: [
              ...(newSelectedFolderId
                ? [
                    {
                      FN: "FLD_Guids_String",
                      FV: newSelectedFolderId,
                      FO: 18,
                    },
                  ]
                : []),
              ...(checkIfStringValueExists(datePeriod)
                ? [
                    {
                      FN: "MSS_TimePeriod",
                      FV: datePeriod,
                      FO: 4,
                    },
                  ]
                : []),
              ...(checkIfStringValueExists(hasAttachments)
                ? [
                    {
                      FN: "MSS_Attachments",
                      FV: hasAttachments,
                      FO: 11,
                    },
                  ]
                : []),
              ...(checkIfStringValueExists(readUnread)
                ? [
                    {
                      FN: "MSS_Read",
                      FV: readUnread,
                      FO: 17,
                    },
                  ]
                : []),
              ...(checkIfStringValueExists(flagged)
                ? [
                    {
                      FN: "MSS_Flagged",
                      FV: flagged,
                      FO: 15,
                    },
                  ]
                : []),
              ...(checkIfStringValueExists(inOut)
                ? [
                    {
                      FN: "MSS_InOut",
                      FV: inOut,
                      FO: 1,
                    },
                  ]
                : []),
              ...(checkIfStringValueExists(from)
                ? [
                    {
                      FN: "MSS_From",
                      FV: from,
                      FO: 7,
                    },
                  ]
                : []),
              ...(checkIfStringValueExists(to)
                ? [
                    {
                      FN: "MSS_To",
                      FV: to,
                      FO: 8,
                    },
                  ]
                : []),
              ...(checkIfStringValueExists(searchText)
                ? [
                    {
                      FN: "MSS_TextValue",
                      FV: escapeQuotedContent(searchText),
                      FO: 9,
                    },
                  ]
                : []),
              ...(checkIfStringValueExists(searchIn)
                ? [
                    {
                      FN: "MSS_TextIn",
                      FV: searchIn,
                      FO: 10,
                    },
                  ]
                : []),
            ],
          };
        }

        if (selectedFolderId) {
          const queryParameters = new URLSearchParams();

          if (!hasFilters) {
            queryParameters.append("FLD_Guid", selectedFolderId);
          }

          queryParameters.append("messageBodyTextFirstChars", "200");
          queryParameters.append("$inlinecount", "allpages");
          queryParameters.append("$top", (pageSize || 100).toString());
          queryParameters.append("$skip", (offset || 0).toString());
          queryParameters.append("$orderby", "MSG_CreatedDateTime desc");

          if (hasFilters) {
            queryParameters.append(
              "jsonFilterCriteria",
              JSON.stringify(jsonFilterCriteria)
            );
          }

          return {
            url: `${domainBaseUrl}/odt/GridMessages2?${queryParameters.toString()}`,
            category: "getGridMessages",
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
            },
          };
        }
      }
    );

  const response$ = sources.HTTP.select("getGridMessages")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200);

  const action1$ = response$
    .compose(
      sampleCombine(
        actionPayload$,
        selectedFolderId$,
        searchFilters$,
        foldersById$
      )
    )
    .map(
      ([
        response,
        actionPayload,
        selectedFolderId,
        searchFilters,
        foldersById,
      ]) => {
        const { offset } = actionPayload?.payload || {};
        const responseBody = response?.body as unknown as ResponseDTO<
          GridMessageResponseDTO[]
        >;

        return fetchGridMessageSuccess({
          responseBody,
          folderGuid: selectedFolderId,
          offset,
          isSearching:
            hasSearchFilters(searchFilters) ||
            isSearchFolderSavedCriteria(foldersById?.[selectedFolderId]),
        });
      }
    );

  const action2$ = response$
    .compose(
      sampleCombine(
        actionPayload$,
        searchFilters$,
        foldersById$,
        selectedFolderId$
      )
    )
    .filter(
      ([
        response,
        actionPayload,
        searchFilters,
        foldersById,
        selectedFolderId,
      ]) => {
        return (
          hasSearchFilters(searchFilters) ||
          isSearchFolderSavedCriteria(foldersById?.[selectedFolderId])
        );
      }
    )
    .map(([response, actionPayload]) => {
      const { offset } = actionPayload?.payload || {};
      const responseBody = response?.body as unknown as ResponseDTO<
        GridMessageResponseDTO[]
      >;

      return setFilteredIds({
        entityType: SEARCH_ENTITIES.messages,
        responseBody,
        offset,
      });
    });

  const action3$ = response$
    .compose(sampleCombine(actionPayload$, searchFilters$))
    .map(([response, actionPayload, searchFilters]) => {
      const { offset } = actionPayload?.payload || {};
      const responseBody = response?.body as unknown as ResponseDTO<
        GridMessageResponseDTO[]
      >;

      return setAppliedSearchFiltersCount({
        entityType: SEARCH_ENTITIES.messages,
        filters: searchFilters,
      });
    });

  const failedActionStream$ = sources.ACTION.filter(
    (action) => action.type === fetchGridMessages.type
  )
    .compose(sampleCombine(selectedFolderId$))
    .filter(([response, selectedFolderId]) => {
      return !selectedFolderId;
    })
    .map(([response, selectedFolderId]) => {
      return fetchGridMessageFailed({
        requestMessage: "Missing selected folder",
      });
    });

  return {
    ACTION: xs.merge(action1$, action2$, action3$, failedActionStream$),
    HTTP: request$,
  };
};

export const getGridMessagesFailed = (sources) => {
  const response$ = sources.HTTP.select("getGridMessages")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = xs
    .combine(response$)
    .map((arr) => fetchGridMessageFailed(arr));
  return {
    ACTION: action$,
  };
};
