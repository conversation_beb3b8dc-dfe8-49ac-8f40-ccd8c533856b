import { CriterionD<PERSON>, CriterionDataDTO } from "../types/DTOs/CriterionDTO";
import { Criterion, CriterionData } from "../types/criterion";

/**
 * Maps a CriterionDTO object to a Criterion object
 * @param criterionDTO - The DTO object from the API
 * @returns Criterion - The transformed object for the application
 */
export const mapCriterion = (criterionDTO: CriterionDTO): Criterion => ({
  createdUserGuid: criterionDTO.CRC_CreatedUserGuid,
  id: criterionDTO.CRC_Guid,
  criteriaId: criterionDTO.CRC_CriteriaId,
  data: parseCriterionData(criterionDTO),
  description: criterionDTO.CRC_Description,
  isDefault: criterionDTO.CRC_IsDefault,
  name: criterionDTO.CRC_Name,
  usrGuid: criterionDTO.CRC_USR_Guid,
  updatedUserGuid: criterionDTO.CRC_UpdatedUserGuid,
  contactCombos: criterionDTO.ContactCombos,
  fldGuid: criterionDTO.FLD_FLD_Guid,
  fldNames: criterionDTO.FLD_Names,
  entityId: criterionDTO.entityId,
});

/**
 * Maps a CriterionDataDTO object to a CriterionData object
 * @param criterionDataDTO - The DTO data object from the API
 * @returns CriterionData - The transformed data object for the application
 */
export const mapCriterionData = (criterionDataDTO: CriterionDataDTO): CriterionData => ({
  // Simple split by comma if value exists, otherwise empty array
  folderIds: criterionDataDTO.FLD_Guids_String ? criterionDataDTO.FLD_Guids_String?.split(',') : [],
  mstId: criterionDataDTO.MSS_MST_Id,
  textValue: criterionDataDTO.MSS_TextValue,
  textIn: criterionDataDTO.MSS_TextIn,
  inOut: criterionDataDTO.MSS_InOut,
  inOutUser: criterionDataDTO.MSS_InOutUser,
  state: criterionDataDTO.MSS_State,
  from: criterionDataDTO.MSS_From,
  to: criterionDataDTO.MSS_To,
  key: criterionDataDTO.MSS_Key,
  findRelated: criterionDataDTO.MSS_FindRelated,
  timePeriod: criterionDataDTO.MSS_TimePeriod,
  dateTimeFrom: criterionDataDTO.MSS_DateTimeFrom,
  dateTimeTo: criterionDataDTO.MSS_DateTimeTo,
  attachments: criterionDataDTO.MSS_Attachments,
  attachmentFileName: criterionDataDTO.MSS_AttachmentFileName,
  attachmentType: criterionDataDTO.MSS_AttachmentType,
  read: criterionDataDTO.MSS_Read,
  categoriesBits: criterionDataDTO.MSS_CategoriesBits,
  important: criterionDataDTO.MSS_Important,
  flagged: criterionDataDTO.MSS_Flagged,
  linkedCase: criterionDataDTO.MSS_LinkedCase,
  metaDataValues: criterionDataDTO.MSS_MetaDataValues,
  metaDataValuesSearchOperator: criterionDataDTO.MetaDataValuesSearchOperator,
  metadataLinkedCases: criterionDataDTO.MSS_MetadataLinkedCases,
  folderGuid: criterionDataDTO.FLD_Guid,
});

/**
 * Helper function to parse the CRC_Data string from CriterionDTO
 * @param criterionDTO - The criterion DTO containing a CRC_Data JSON string
 * @returns CriterionData - The parsed data object
 */
export const parseCriterionData = (criterionDTO: CriterionDTO): CriterionData => {
  try {
    const dataDTO = JSON.parse(criterionDTO.CRC_Data) as CriterionDataDTO;
    return mapCriterionData(dataDTO);
  } catch (error) {
    console.error('Failed to parse criterion data:', error);
    // Return empty/default data on error
    return {
      folderIds: [],
      mstId: null,
      textValue: null,
      textIn: [],
      inOut: '',
      inOutUser: null,
      state: null,
      from: null,
      to: null,
      key: null,
      findRelated: null,
      timePeriod: '',
      dateTimeFrom: null,
      dateTimeTo: null,
      attachments: '',
      attachmentFileName: null,
      attachmentType: null,
      read: null,
      categoriesBits: null,
      important: null,
      flagged: '',
      linkedCase: null,
      metaDataValues: null,
      metaDataValuesSearchOperator: null,
      metadataLinkedCases: null,
      folderGuid: null,
    };
  }
};
