/**
 * Converts plain text to HTML-friendly text by preserving line breaks and formatting
 * @param text Plain text input
 * @returns Text with HTML line breaks
 */
export const convertPlainTextToHtml = (text: string): string => {
  if (!text) return '';

  // First, normalize the input to handle different line break styles
  const normalizedText = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  // Step 1: Escape HTML special characters to prevent XSS
  let htmlText = normalizedText
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');

  // Step 2: Handle line breaks
  // Special handling for consecutive line breaks (empty lines)
  // Use paragraph tags for better email client compatibility
  let processedText = htmlText;

  // Handle empty lines by splitting on double newlines and creating proper paragraph structure
  if (processedText.includes('\n\n')) {
    // Split text by single newlines first to preserve all line structure
    const lines = processedText?.split('\n');
    const result: string[] = [];
    let currentParagraph: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      if (line.trim() === '') {
        // Empty line found
        if (currentParagraph.length > 0) {
          // Close current paragraph
          result.push(`<p>${currentParagraph.join('<br>')}</p>`);
          currentParagraph = [];
        }
        // Add empty paragraph
        result.push('<p>&nbsp;</p>');
      } else {
        // Non-empty line
        currentParagraph.push(line);
      }
    }

    // Close any remaining paragraph
    if (currentParagraph.length > 0) {
      result.push(`<p>${currentParagraph.join('<br>')}</p>`);
    }

    processedText = result.join('');
  } else {
    // If there are no empty lines, just replace all newlines with <br> tags
    processedText = processedText.replace(/\n/g, '<br>');
  }

  htmlText = processedText;

  // Step 3: Handle consecutive spaces (optional)
  // In HTML, consecutive spaces are collapsed to a single space
  // To preserve multiple spaces, replace them with &nbsp;
  htmlText = htmlText.replace(/ {2,}/g, (match) => {
    return '&nbsp;'.repeat(match.length);
  });

  return htmlText;
};

/**
 * Converts HTML text back to plain text, preserving line breaks and formatting
 * @param htmlText HTML text input
 * @returns Plain text with proper line breaks
 */
export const convertHtmlToPlainText = (htmlText: string): string => {
  if (!htmlText) return '';

  // First, normalize the input to handle different line break styles
  const normalizedHtml = htmlText.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  // Special case: If the content appears to be plain text with HTML line breaks
  // This handles cases where the server might be storing plain text with <br> tags
  if (normalizedHtml.includes('<br') && !/<[a-z][^>]*>/i.test(normalizedHtml.replace(/<br[^>]*>/gi, ''))) {
    // Just replace <br> tags with newlines and return
    const plainText = normalizedHtml
      .replace(/<br\s*\/?>/gi, '\n')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#039;/g, "'");

    return plainText;
  }

  // Step 1: Handle paragraph tags specially to preserve empty lines
  // Process paragraphs to properly handle consecutive empty paragraphs
  let plainText = normalizedHtml;

  // First, handle the conversion by processing each paragraph individually
  // This approach ensures we properly count empty paragraphs
  const paragraphMatches = plainText.match(/<p[^>]*>.*?<\/p>/gi);
  if (paragraphMatches) {
    const processedParagraphs: string[] = [];

    for (const paragraph of paragraphMatches) {
      // Check if this is an empty paragraph
      const content = paragraph.replace(/<p[^>]*>(.*?)<\/p>/gi, '$1').trim();
      if (content === '&nbsp;' || content === '') {
        // Empty paragraph - represents an empty line
        processedParagraphs.push('');
      } else {
        // Non-empty paragraph - convert <br> tags to newlines and add content
        const paragraphContent = content.replace(/<br\s*\/?>/gi, '\n');
        processedParagraphs.push(paragraphContent);
      }
    }

    // Join paragraphs with proper spacing
    // Empty paragraphs should only add one newline, non-empty paragraphs should be separated by double newlines
    let result = '';
    for (let i = 0; i < processedParagraphs.length; i++) {
      const paragraph = processedParagraphs[i];

      if (i > 0) {
        // Add separator before this paragraph
        if (paragraph === '' || processedParagraphs[i - 1] === '') {
          // If current or previous paragraph is empty, add single newline
          result += '\n';
        } else {
          // Both paragraphs have content, add double newline
          result += '\n\n';
        }
      }

      result += paragraph;
    }

    plainText = result;
  } else {
    // Fallback to original logic if no paragraphs found
    plainText = normalizedHtml
      .replace(/<p[^>]*>\s*&nbsp;\s*<\/p>/gi, '\n\n')
      .replace(/<p[^>]*>\s*<\/p>/gi, '\n\n')
      // Then replace </p><p> with double newlines to represent paragraph breaks
      .replace(/<\/p>\s*<p[^>]*>/gi, '\n\n');
  }

  // Step 2: Replace other block-level elements with line breaks (skip paragraphs since we handled them above)
  if (!paragraphMatches) {
    plainText = plainText
      .replace(/<\/(p|div|h[1-6]|blockquote|pre|table|tr|ul|ol)>/gi, '\n')
      .replace(/<(p|div|h[1-6]|blockquote|pre|table|tr|ul|ol)[^>]*>/gi, '\n');
  } else {
    // Only handle non-paragraph block elements
    plainText = plainText
      .replace(/<\/(div|h[1-6]|blockquote|pre|table|tr|ul|ol)>/gi, '\n')
      .replace(/<(div|h[1-6]|blockquote|pre|table|tr|ul|ol)[^>]*>/gi, '\n');
  }

  // Step 3: Replace list items with a newline and bullet
  plainText = plainText
    .replace(/<li[^>]*>/gi, '\n• ');

  // Step 4: Replace <br>, <br/>, <br /> tags with newlines
  // This is crucial for preserving line breaks that were entered by the user
  plainText = plainText
    .replace(/<br\s*\/?>/gi, '\n');

  // Step 5: Replace &nbsp; with spaces
  plainText = plainText.replace(/&nbsp;/g, ' ');

  // Step 6: Replace common HTML entities with their characters
  plainText = plainText
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#039;/g, "'");

  // Step 7: Remove any remaining HTML tags
  plainText = plainText.replace(/<[^>]*>/g, '');

  // Step 8: Preserve consecutive line breaks
  // DO NOT collapse multiple line breaks - this is key for preserving empty lines
  // Just trim leading/trailing whitespace
  plainText = plainText.trim();

  return plainText;
};
