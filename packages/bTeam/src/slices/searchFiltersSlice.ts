import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { SEARCH_ENTITIES, SearchEntityType } from "../constants/searchEntities";
import { CriterionDTO } from "../types/DTOs/CriterionDTO";
import { SEARCH_FIELD_API_NAMES } from "../constants/searchFieldsMessage";
import { parseCriteriaData } from "../helpers/searchFolderSavedCriteria";

// appliedSearchFiltersCount is populated after API request.
// So based on this we know if the user is currently searching.
const initialState = {
  [SEARCH_ENTITIES.messages]: {
    searchFilters: {},
    appliedSearchFiltersCount: 0,
    filteredIds: [],
    isQuickSearchEnabled: false,
  },
  [SEARCH_ENTITIES.notifications]: {
    searchFilters: {},
    appliedSearchFiltersCount: 0,
    filteredIds: [],
    isQuickSearchEnabled: false,
  },
};

const searchFiltersSlice = createSlice({
  name: "bTeamSearchFiltersSlice",
  initialState,
  reducers: {
    setIsQuickSearchEnabled(
      state,
      action: PayloadAction<{
        entityType: SearchEntityType;
        isEnabled: boolean;
      }>
    ) {
      const { entityType, isEnabled } = action.payload;

      // Runtime validation
      if (!state[entityType]) {
        return;
      }

      state[entityType].isQuickSearchEnabled = isEnabled;
    },
    setSearchFilters(
      state,
      action: PayloadAction<{ entityType: SearchEntityType; filters: any }>
    ) {
      const { entityType, filters } = action.payload;

      // Runtime validation
      if (!state[entityType]) {
        return;
      }

      state[entityType].searchFilters = {
        ...state[entityType].searchFilters,
        ...filters,
      };
    },
    setAppliedSearchFiltersCount(
      state,
      action: PayloadAction<{ entityType: SearchEntityType; filters: any }>
    ) {
      const { entityType, filters } = action.payload;

      // Runtime validation
      if (!state[entityType]) {
        return;
      }

      // Counter of how many filters have value
      const searchFilterCount = Object.entries(filters).reduce(
        (count, [key, value]) => {
          const isValueSet =
            value !== null &&
            value !== undefined &&
            value !== "" &&
            value !== "NotSet";

          return count + (isValueSet ? 1 : 0);
        },
        0
      );

      state[entityType].appliedSearchFiltersCount = searchFilterCount;
    },
    setFilteredIds(
      state,
      action: PayloadAction<{
        entityType: SearchEntityType;
        responseBody: any;
        offset?: boolean;
      }>
    ) {
      const { entityType, responseBody, offset } = action.payload || {};

      // Runtime validation
      if (!state[entityType]) {
        return;
      }

      let newFilteredIds;

      if (entityType === SEARCH_ENTITIES.messages) {
        newFilteredIds = responseBody.value.map((message) => message.UMS_Guid);
      }

      if (entityType === SEARCH_ENTITIES.notifications) {
        newFilteredIds = responseBody.value.map(
          (notificationItem) => notificationItem.CMM_Guid
        );
      }

      state[entityType].filteredIds = offset
        ? Array.from(
            new Set([...state[entityType].filteredIds, ...newFilteredIds])
          )
        : newFilteredIds;
    },
    clearSearchFields(
      state,
      action: PayloadAction<{ entityType: SearchEntityType }>
    ) {
      const { entityType } = action.payload;

      // Runtime validation
      if (!state[entityType]) {
        return;
      }

      state[entityType].searchFilters = {};
      state[entityType].appliedSearchFiltersCount = 0;
      state[entityType].filteredIds = [];
      state[entityType].isQuickSearchEnabled = false;
    },
    setSearchFiltersBasedOnSavedCriteria(
      state,
      action: PayloadAction<{
        entityType: SearchEntityType;
        responseBody: CriterionDTO;
      }>
    ) {
      const { entityType } = action.payload;

      // Runtime validation
      if (!state[entityType]) {
        return;
      }

      const newSearchFilters = {};
      let appliedCount = 0;

      if (entityType === SEARCH_ENTITIES.messages) {
        const processedCriteria = parseCriteriaData(
          action.payload.responseBody
        );

        // Map API field names to app field names and set values
        Object.entries(SEARCH_FIELD_API_NAMES).forEach(
          ([apiField, appField]) => {
            let value = processedCriteria.CRC_Data[apiField];

            // Skip null, undefined, or empty values
            if (value === null || value === undefined || value === "") {
              return;
            }

            // Convert MSS_TextIn array to comma-separated string if it's an array
            if (apiField === "MSS_TextIn" && Array.isArray(value)) {
              value = value.join(",");
            }

            newSearchFilters[appField] = value;
            appliedCount++;
          }
        );
      }

      state[entityType].searchFilters = newSearchFilters;
      state[entityType].appliedSearchFiltersCount = appliedCount;
    },
  },
});

export const {
  setIsQuickSearchEnabled,
  setSearchFilters,
  setAppliedSearchFiltersCount,
  setFilteredIds,
  clearSearchFields,
  setSearchFiltersBasedOnSavedCriteria,
} = searchFiltersSlice.actions;

export default searchFiltersSlice.reducer;
