import { createSlice } from "@reduxjs/toolkit";
import { NotificationDTO } from "../types/DTOs/NotificationDTO";

const SUGGESTIONS_SET_LIMIT = 50;

const initialState = {
  count: 0,
  notificationsAllIds: [],
  notificationsIsLoading: false,
  notificationsIsLoadingListMore: false,
  notificationsErrorText: null,
  offset: 0,
  searchSuggestions: [],
};

const notificationsSlice = createSlice({
  name: "notificationsSlice",
  initialState,
  reducers: {
    fetchGridNotifications(state, { payload }) {
      state.notificationsErrorText = null;

      if (payload?.offset) {
        state.notificationsIsLoadingListMore = true;
      } else {
        state.notificationsIsLoading = true;
      }
    },
    fetchGridNotificationsSuccess(state, action) {
      const { responseBody, offset } = action.payload;
      let newIds: string[] = [];

      responseBody.value.forEach((notification: NotificationDTO) => {
        const { CMM_Guid } = notification;
        newIds.push(CMM_Guid);
      });

      state.notificationsAllIds = Array.from(
        new Set([...state.notificationsAllIds, ...newIds])
      );
      state.count = parseInt(responseBody["odata.count"], 10) || 0;
      state.notificationsIsLoading = false;
      state.notificationsIsLoadingListMore = false;
      state.notificationsErrorText = null;
      state.offset = offset ? state.offset + offset : state.offset;
    },
    fetchGridNotificationsFailed(state, { payload }) {
      state.notificationsErrorText =
        payload[0]?.response?.body["odata.error"]?.message?.value ||
        "Error fetching inbox items";
      state.notificationsIsLoading = false;
      state.notificationsIsLoadingListMore = false;
    },
    clearOffset(state) {
      state.offset = 0;
    },
    addSearchSuggestion: (state, { payload }) => {
      if (!payload.trim()) return state;

      // Check if suggestion already exists, if so, remove it
      // Limit to max {SUGGESTIONS_SET_LIMIT} items
      state.searchSuggestions = [
        payload,
        ...state.searchSuggestions.filter(
          (suggestion) => suggestion !== payload
        ),
      ].slice(0, SUGGESTIONS_SET_LIMIT);
    },
  },
});

export const {
  fetchGridNotifications,
  fetchGridNotificationsSuccess,
  fetchGridNotificationsFailed,
  clearOffset,
  addSearchSuggestion,
} = notificationsSlice.actions;

export default notificationsSlice.reducer;
