export const NOTIFICATIONS_SEARCH_FIELD_NAMES = {
  searchText: "searchText",
  createDateFrom: "createDateFrom",
  createDateTo: "createDateTo",
  from: "from",
  participants: "participants",
  notifiedUsers: "notifiedUsers",
  readStatus: "readStatus",
  notified: "notified",
  requestForReply: "requestForReply",
  isStarred: "isStarred",
};

export const NOTIFICATIONS_SEARCH_FIELD_API_NAMES = {
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.searchText]: "SearchText",
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.createDateFrom]: "CreateDateFrom",
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.createDateTo]: "CreateDateTo",
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.from]: "ReceivedFromUsers",
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.participants]: "Participants",
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.notifiedUsers]: "Notified",
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.readStatus]: "ReadUnread",
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.notified]: "NotifiedStatus",
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.requestForReply]: "CommentRequestForReplyStatus",
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.isStarred]: "IsStarred",
};

export const NOTIFICATIONS_SEARCH_API_FIELD_NAMES = {
  [NOTIFICATIONS_SEARCH_FIELD_API_NAMES.searchText]: [
    NOTIFICATIONS_SEARCH_FIELD_NAMES.searchText,
  ],
  [NOTIFICATIONS_SEARCH_FIELD_API_NAMES.createDateFrom]: [
    NOTIFICATIONS_SEARCH_FIELD_NAMES.createDateFrom,
  ],
  [NOTIFICATIONS_SEARCH_FIELD_API_NAMES.createDateTo]: [
    NOTIFICATIONS_SEARCH_FIELD_NAMES.createDateTo,
  ],
  [NOTIFICATIONS_SEARCH_FIELD_API_NAMES.from]: [
    NOTIFICATIONS_SEARCH_FIELD_NAMES.from,
  ],
  [NOTIFICATIONS_SEARCH_FIELD_API_NAMES.participants]: [
    NOTIFICATIONS_SEARCH_FIELD_NAMES.participants,
  ],
  [NOTIFICATIONS_SEARCH_FIELD_API_NAMES.notifiedUsers]: [
    NOTIFICATIONS_SEARCH_FIELD_NAMES.notifiedUsers,
  ],
  [NOTIFICATIONS_SEARCH_FIELD_API_NAMES.readStatus]: [
    NOTIFICATIONS_SEARCH_FIELD_NAMES.readStatus,
  ],
  [NOTIFICATIONS_SEARCH_FIELD_API_NAMES.notified]: [
    NOTIFICATIONS_SEARCH_FIELD_NAMES.notified,
  ],
  [NOTIFICATIONS_SEARCH_FIELD_API_NAMES.requestForReply]: [
    NOTIFICATIONS_SEARCH_FIELD_NAMES.requestForReply,
  ],
  [NOTIFICATIONS_SEARCH_FIELD_API_NAMES.isStarred]: [
    NOTIFICATIONS_SEARCH_FIELD_NAMES.isStarred,
  ],
};

export const READ_STATUS_OPTIONS = {
  read: {
    name: "Read",
    value: "1",
  },
  unread: {
    name: "Unread",
    value: "2",
  },
  notSet: {
    name: "Not Set",
    value: null,
  },
};

export const NOTIFIED_OPTIONS = {
  yes: {
    name: "Yes",
    value: "1",
  },
  no: {
    name: "No",
    value: "2",
  },
  notSet: {
    name: "Not Set",
    value: null,
  },
};

export const REQUEST_FOR_REPLY_OPTIONS = {
  requestByMePending: {
    name: "Request By Me Pending",
    value: "1",
  },
  requestByMeCompleted: {
    name: "Request By Me Completed",
    value: "2",
  },
  requestToMePending: {
    name: "Request To Me Pending",
    value: "3",
  },
  requestToMeCompleted: {
    name: "Request To Me Completed",
    value: "4",
  },
  notSet: {
    name: "Not Set",
    value: null,
  },
};

export const IS_STARRED_OPTIONS = {
  yes: {
    name: "Yes",
    value: "true",
  },
  // no: {
  //   name: "No",
  //   value: "false",
  // },
  notSet: {
    name: "Not Set",
    value: null,
  },
};

export const NOTIFICATIONS_SEARCH_FIELD_NAMES_OPTIONS = {
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.readStatus]:
    Object.values(READ_STATUS_OPTIONS),
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.notified]: Object.values(NOTIFIED_OPTIONS),
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.requestForReply]: Object.values(
    REQUEST_FOR_REPLY_OPTIONS
  ),
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.isStarred]:
    Object.values(IS_STARRED_OPTIONS),
};

export const NOTIFICATIONS_SEARCH_FIELD_INITIAL_VALUE = {
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.searchText]: "",
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.createDateFrom]: "",
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.createDateTo]: "",
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.from]: [],
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.participants]: [],
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.notifiedUsers]: [],
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.readStatus]:
  READ_STATUS_OPTIONS.notSet.value,
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.notified]: NOTIFIED_OPTIONS.yes.value,
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.requestForReply]:
  REQUEST_FOR_REPLY_OPTIONS.notSet.value,
  [NOTIFICATIONS_SEARCH_FIELD_NAMES.isStarred]: IS_STARRED_OPTIONS.notSet.value,
};
