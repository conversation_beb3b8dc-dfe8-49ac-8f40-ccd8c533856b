import React, { useState } from "react";
import {
  Alert,
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
} from "react-native";
import { pick, types, errorCodes, isErrorWithCode } from "@react-native-documents/picker";

// Styles
import { SPACING, FONTSIZES } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

import Dropdown from "./dropdown";
import DateTimeDropDown from "./dateTimeDropdown";
import ParticipantSelectionField from "./participantSelectionField";
import AttachmentsField from "./attachmentsField";
import MemoAttachment from "./memoAttachment";

const expiryOptions = [
  { id: 1, name: "No expiration" },
  { id: 2, name: "Duration" },
  { id: 3, name: "Date & Time" },
];

type Props = {
  notifyList: [
    {
      id: number;
      name: string;
      avatar: string;
      occupation: string;
    }
  ];
};

let newNotifyList = { allIds: [] };

const DurationDropdowns = () => {
  const durationUnits = [
    { id: 1, name: "Days" },
    { id: 2, name: "Months" },
  ];

  const [selectedUnit, setSelectedUnit] = useState(durationUnits[0]);
  const [selectedValue, setSelectedValue] = useState({ id: 1, name: "1" });

  const generateDurationOptions = (unit) => {
    const max = unit.name === "Days" ? 31 : 12;

    return Array.from({ length: max }, (_, i) => ({
      id: i + 1,
      name: (i + 1).toString(),
    }));
  };

  const durationValues = generateDurationOptions(selectedUnit);

  return (
    <View style={{ flexDirection: "row" }}>
      <Dropdown
        options={durationUnits}
        selectedOption={selectedUnit}
        setSelectedOption={setSelectedUnit}
      />
      <Dropdown
        options={durationValues}
        selectedOption={selectedValue}
        setSelectedOption={setSelectedValue}
      />
    </View>
  );
};

const NewMemoScreen = ({ notifyList }: Props) => {
  const { styles, color, images } = useThemeAwareObject(createStyles);
  const [selectedExpiryOption, setExpirySelectedOption] = useState(
    expiryOptions[0]
  );
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedDocuments, setSelectedDocuments] = useState([]);
  const [selectedParticipants, setSelectedParticipants] = useState([]);
  const [requestReply, setRequestReply] = useState([]);

  const handleFilePicker = async () => {
    try {
      const result = await pick({
        type: [types.allFiles],
      });

      setSelectedDocuments([...selectedDocuments, result]);
    } catch (err) {
      if (isErrorWithCode(err) && err.code === errorCodes.OPERATION_CANCELED) {
      } else {
        Alert.alert("Error", "Something went wrong");
        throw err;
      }
    }
  };

  const handleSelectParticipants = (participantId) => {
    if (selectedParticipants.indexOf(participantId) === -1) {
      setSelectedParticipants([...selectedParticipants, participantId]);
      newNotifyList = {
        ...newNotifyList,
        [participantId]: {
          id: notifyList[participantId]?.id,
          name: notifyList[participantId]?.name,
          avatar: notifyList[participantId]?.avatar,
          occupation: notifyList[participantId]?.occupation,
        },
        allIds:
          newNotifyList.allIds.indexOf(participantId) === -1
            ? [...newNotifyList.allIds, participantId]
            : newNotifyList.allIds,
      };
    } else {
      setSelectedParticipants(
        selectedParticipants.filter((id) => id !== participantId)
      );

      delete newNotifyList[participantId];
      newNotifyList.allIds = newNotifyList.allIds.filter(
        (id) => id !== participantId
      );
    }
  };

  const handleSelectReply = (participantId) => {
    requestReply.indexOf(participantId) === -1
      ? setRequestReply([...requestReply, participantId])
      : setRequestReply(requestReply.filter((id) => id !== participantId));
  };

  return (
    <KeyboardAvoidingView behavior="padding" style={styles.container}>
      {/* Form */}
      <ScrollView>
        <View style={styles.inputWrapper}>
          <TextInput
            placeholder="Title"
            placeholderTextColor={color.TEXT_DIMMED}
            style={{ color: color.TEXT_DEFAULT }}
          />
        </View>

        <ParticipantSelectionField
          title="Notified"
          notifyList={notifyList}
          selectedParticipants={selectedParticipants}
          handleSelectParticipants={handleSelectParticipants}
        />

        <ParticipantSelectionField
          title="Request Reply"
          notifyList={newNotifyList}
          selectedParticipants={requestReply}
          handleSelectParticipants={handleSelectReply}
        />

        <View style={[styles.inputWrapper, styles.flexRowBetweenCenter]}>
          <Text style={{ color: color.TEXT_DIMMED }}>Reply Expiration</Text>
          {/* TODO: Keep for now */}
          {/* <NotifyRecipient onRecipientsChange={handleRecipientsChange} /> */}

          <View
            style={{
              flexDirection: "row",
              flexWrap: "wrap",
              alignItems: "center",
            }}
          >
            <Dropdown
              options={expiryOptions}
              selectedOption={selectedExpiryOption}
              setSelectedOption={setExpirySelectedOption}
            />
          </View>
        </View>

        {selectedExpiryOption.id !== 1 && (
          <View style={[styles.inputWrapper, styles.flexRowBetweenCenter]}>
            <Text style={{ color: color.TEXT_DIMMED }}>
              {selectedExpiryOption?.name}
            </Text>

            {selectedExpiryOption.id === 2 ? (
              <DurationDropdowns />
            ) : (
              <DateTimeDropDown
                selectedDate={selectedDate}
                setSelectedDate={setSelectedDate}
              />
            )}
          </View>
        )}

        <View style={styles.inputWrapper}>
          <TextInput
            placeholder="Description"
            placeholderTextColor={color.TEXT_DIMMED}
            style={{ height: 70, color: color.TEXT_DEFAULT }}
            multiline
            numberOfLines={3}
          />
        </View>

        <AttachmentsField handleFilePicker={handleFilePicker} />

        {selectedDocuments && (
          <View style={{ flex: 1, padding: SPACING.XS }}>
            {selectedDocuments?.flatMap((doc) => (
              <MemoAttachment
                key={doc[0]?.uri}
                doc={doc}
                selectedDocuments={selectedDocuments}
                setSelectedDocuments={setSelectedDocuments}
              />
            ))}
          </View>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default NewMemoScreen;

const createStyles = ({ color, images }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
    inputWrapper: {
      padding: SPACING.M,
      borderBottomColor: color.BORDER_COLOR,
      borderBottomWidth: 1,
    },
    flexRowBetweenCenter: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    placeholder: {
      backgroundColor: color.PRESSABLE,
      marginRight: SPACING.M,
      borderRadius: 50,
    },
  });

  return { styles, color, images };
};
