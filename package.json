{"name": "benefitmobile", "version": "1.0.0", "description": "Benefit Mobile Apps", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "postinstall": "npx patch-package"}, "workspaces": {"packages": ["packages/**"], "nohoist": ["packages/engine/ios"]}, "dependencies": {"react-native": "0.78.3", "b-ui-lib": "file:b-ui-lib-0.1.33.tgz"}, "repository": {"type": "git", "url": "<EMAIL>:v3/benefitsoftware/bSuite2/bSeafarer"}, "author": "PaperGo", "license": "ISC", "devDependencies": {"react-native-dotenv": "^3.4.8"}}